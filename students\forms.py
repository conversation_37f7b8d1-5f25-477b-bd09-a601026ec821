from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from .models import (
    Student, Parent, Class, Grade, StudentDocument, VacationRequest,
    StudentInfraction, StudentTransfer, StudentAttachment, ElectronicRegistration
)
from accounts.models import User


class StudentForm(forms.ModelForm):
    """
    Student creation and update form
    """
    class Meta:
        model = Student
        fields = [
            'student_id', 'admission_number', 'first_name', 'last_name',
            'first_name_ar', 'last_name_ar', 'date_of_birth', 'gender',
            'nationality', 'national_id', 'passport_number', 'blood_type',
            'parent', 'current_class', 'admission_date', 'previous_school',
            'medical_conditions', 'allergies', 'special_needs', 'photo'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'admission_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'medical_conditions': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'allergies': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'special_needs': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'photo': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('student_id', css_class='form-group col-md-6 mb-3'),
                    Column('admission_number', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('first_name', css_class='form-group col-md-6 mb-3'),
                    Column('last_name', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('first_name_ar', css_class='form-group col-md-6 mb-3'),
                    Column('last_name_ar', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Personal Details'),
                Row(
                    Column('date_of_birth', css_class='form-group col-md-4 mb-3'),
                    Column('gender', css_class='form-group col-md-4 mb-3'),
                    Column('blood_type', css_class='form-group col-md-4 mb-3'),
                ),
                Row(
                    Column('nationality', css_class='form-group col-md-6 mb-3'),
                    Column('national_id', css_class='form-group col-md-6 mb-3'),
                ),
                'passport_number',
                'photo',
            ),
            Fieldset(
                _('Academic Information'),
                Row(
                    Column('parent', css_class='form-group col-md-6 mb-3'),
                    Column('current_class', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('admission_date', css_class='form-group col-md-6 mb-3'),
                    Column('previous_school', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Medical Information'),
                'medical_conditions',
                'allergies',
                'special_needs',
            ),
            FormActions(
                Submit('submit', _('Save Student'), css_class='btn btn-primary'),
                HTML('<a href="{% url "students:list" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )


class ParentForm(forms.ModelForm):
    """
    Parent creation and update form
    """
    class Meta:
        model = Parent
        fields = [
            'father_name', 'mother_name', 'father_phone', 'mother_phone',
            'home_address', 'emergency_phone'
        ]
        widgets = {
            'home_address': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class VacationRequestForm(forms.ModelForm):
    """
    Vacation request form
    """
    class Meta:
        model = VacationRequest
        fields = ['student', 'start_date', 'end_date', 'reason']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date cannot be after end date.'))

        return cleaned_data


class StudentInfractionForm(forms.ModelForm):
    """
    Student infraction form
    """
    class Meta:
        model = StudentInfraction
        fields = [
            'student', 'incident_date', 'description', 'severity',
            'action_taken', 'follow_up_required', 'follow_up_date', 'notes'
        ]
        widgets = {
            'incident_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'follow_up_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class StudentTransferForm(forms.ModelForm):
    """
    Student transfer form
    """
    class Meta:
        model = StudentTransfer
        fields = [
            'student', 'transfer_type', 'from_school', 'to_school',
            'from_class', 'to_class', 'transfer_date', 'reason', 'notes'
        ]
        widgets = {
            'transfer_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class StudentDocumentForm(forms.ModelForm):
    """
    Student document form
    """
    class Meta:
        model = StudentDocument
        fields = ['student', 'document_type', 'title', 'file', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'


class StudentAttachmentForm(forms.ModelForm):
    """
    Student attachment form
    """
    class Meta:
        model = StudentAttachment
        fields = ['student', 'title', 'attachment_type', 'file', 'description', 'is_public']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'


class ElectronicRegistrationForm(forms.ModelForm):
    """
    Electronic registration form for online student registration
    """
    class Meta:
        model = ElectronicRegistration
        fields = [
            'first_name', 'last_name', 'first_name_ar', 'last_name_ar',
            'date_of_birth', 'gender', 'nationality', 'father_name',
            'father_phone', 'father_email', 'mother_name', 'mother_phone',
            'home_address', 'desired_grade', 'previous_school'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'home_address': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Student Information'),
                Row(
                    Column('first_name', css_class='form-group col-md-6 mb-3'),
                    Column('last_name', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('first_name_ar', css_class='form-group col-md-6 mb-3'),
                    Column('last_name_ar', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('date_of_birth', css_class='form-group col-md-4 mb-3'),
                    Column('gender', css_class='form-group col-md-4 mb-3'),
                    Column('nationality', css_class='form-group col-md-4 mb-3'),
                ),
            ),
            Fieldset(
                _('Parent Information'),
                Row(
                    Column('father_name', css_class='form-group col-md-6 mb-3'),
                    Column('father_phone', css_class='form-group col-md-6 mb-3'),
                ),
                'father_email',
                Row(
                    Column('mother_name', css_class='form-group col-md-6 mb-3'),
                    Column('mother_phone', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Address & Academic Information'),
                'home_address',
                Row(
                    Column('desired_grade', css_class='form-group col-md-6 mb-3'),
                    Column('previous_school', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            FormActions(
                Submit('submit', _('Submit Registration'), css_class='btn btn-primary'),
            )
        )


class ClassForm(forms.ModelForm):
    """
    Class creation and update form
    """
    class Meta:
        model = Class
        fields = ['name', 'grade', 'academic_year', 'class_teacher', 'max_students', 'room_number']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class GradeForm(forms.ModelForm):
    """
    Grade creation and update form
    """
    class Meta:
        model = Grade
        fields = ['name', 'name_ar', 'level', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
