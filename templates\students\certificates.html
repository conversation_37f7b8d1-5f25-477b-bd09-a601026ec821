{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Student Certificates" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .certificate-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .certificate-card:hover {
        transform: translateY(-2px);
    }
    .certificate-preview {
        width: 100%;
        height: 200px;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 10px;
        position: relative;
        overflow: hidden;
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        margin-bottom: 1rem;
    }
    .certificate-preview::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }
    @keyframes float {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }
    .certificate-title {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        z-index: 1;
    }
    .certificate-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        z-index: 1;
    }
    .certificate-type {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
        font-size: 0.7rem;
        z-index: 1;
    }
    .stats-widget {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
    .template-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }
    .template-item {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    .template-item:hover {
        border-color: #007bff;
        background: #f8f9fa;
    }
    .template-item.selected {
        border-color: #28a745;
        background: #d4edda;
    }
    .certificate-status {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
    }
    .status-generated {
        background-color: #d4edda;
        color: #155724;
    }
    .status-issued {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    .status-expired {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-certificate text-primary me-2"></i>{% trans "Student Certificates" %}
                    </h2>
                    <p class="text-muted">{% trans "Generate and manage student certificates and awards" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newCertificateModal">
                        <i class="fas fa-plus me-2"></i>{% trans "New Certificate" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-download me-2"></i>{% trans "Bulk Export" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-certificate fa-2x mb-2"></i>
                <h3 class="mb-1">245</h3>
                <p class="mb-0">{% trans "Total Certificates" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-award fa-2x mb-2"></i>
                <h3 class="mb-1">89</h3>
                <p class="mb-0">{% trans "Achievement Awards" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                <h3 class="mb-1">156</h3>
                <p class="mb-0">{% trans "Completion Certificates" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-medal fa-2x mb-2"></i>
                <h3 class="mb-1">34</h3>
                <p class="mb-0">{% trans "Honor Certificates" %}</p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card certificate-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">{% trans "Filter Certificates" %}</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="student" class="form-label">{% trans "Student" %}</label>
                            <select class="form-select" id="student" name="student">
                                <option value="">{% trans "All Students" %}</option>
                                <option value="1">أحمد محمد علي - STU001</option>
                                <option value="2">فاطمة أحمد حسن - STU002</option>
                                <option value="3">محمد عبدالله سالم - STU003</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="type" class="form-label">{% trans "Certificate Type" %}</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">{% trans "All Types" %}</option>
                                <option value="achievement">{% trans "Achievement" %}</option>
                                <option value="completion">{% trans "Completion" %}</option>
                                <option value="honor">{% trans "Honor Roll" %}</option>
                                <option value="participation">{% trans "Participation" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="generated">{% trans "Generated" %}</option>
                                <option value="issued">{% trans "Issued" %}</option>
                                <option value="pending">{% trans "Pending" %}</option>
                                <option value="expired">{% trans "Expired" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Certificates Grid -->
    <div class="row">
        <!-- Sample Certificates -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card certificate-card">
                <div class="card-body">
                    <div class="certificate-preview">
                        <div class="certificate-type">{% trans "Achievement" %}</div>
                        <div class="certificate-title">شهادة تقدير</div>
                        <div class="certificate-subtitle">للطالب المتميز</div>
                        <div class="certificate-subtitle">أحمد محمد علي</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">{% trans "Excellence Award" %}</h6>
                        <span class="certificate-status status-issued">{% trans "Issued" %}</span>
                    </div>
                    <p class="text-muted small mb-3">{% trans "Issued on" %}: 2025-01-13</p>
                    <div class="btn-group btn-group-sm w-100">
                        <button class="btn btn-outline-primary" title="{% trans 'Preview' %}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" title="{% trans 'Download' %}">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-info" title="{% trans 'Print' %}">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card certificate-card">
                <div class="card-body">
                    <div class="certificate-preview" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <div class="certificate-type">{% trans "Completion" %}</div>
                        <div class="certificate-title">شهادة إتمام</div>
                        <div class="certificate-subtitle">دورة الحاسوب</div>
                        <div class="certificate-subtitle">فاطمة أحمد حسن</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">{% trans "Computer Course" %}</h6>
                        <span class="certificate-status status-generated">{% trans "Generated" %}</span>
                    </div>
                    <p class="text-muted small mb-3">{% trans "Generated on" %}: 2025-01-12</p>
                    <div class="btn-group btn-group-sm w-100">
                        <button class="btn btn-outline-primary" title="{% trans 'Preview' %}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" title="{% trans 'Download' %}">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-info" title="{% trans 'Print' %}">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card certificate-card">
                <div class="card-body">
                    <div class="certificate-preview" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                        <div class="certificate-type">{% trans "Honor" %}</div>
                        <div class="certificate-title">شهادة شرف</div>
                        <div class="certificate-subtitle">قائمة الشرف</div>
                        <div class="certificate-subtitle">محمد عبدالله سالم</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">{% trans "Honor Roll" %}</h6>
                        <span class="certificate-status status-pending">{% trans "Pending" %}</span>
                    </div>
                    <p class="text-muted small mb-3">{% trans "Created on" %}: 2025-01-11</p>
                    <div class="btn-group btn-group-sm w-100">
                        <button class="btn btn-outline-primary" title="{% trans 'Preview' %}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" title="{% trans 'Download' %}">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-info" title="{% trans 'Print' %}">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card certificate-card">
                <div class="card-body">
                    <div class="certificate-preview" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                        <div class="certificate-type">{% trans "Participation" %}</div>
                        <div class="certificate-title">شهادة مشاركة</div>
                        <div class="certificate-subtitle">مسابقة العلوم</div>
                        <div class="certificate-subtitle">سارة محمد أحمد</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">{% trans "Science Competition" %}</h6>
                        <span class="certificate-status status-issued">{% trans "Issued" %}</span>
                    </div>
                    <p class="text-muted small mb-3">{% trans "Issued on" %}: 2025-01-10</p>
                    <div class="btn-group btn-group-sm w-100">
                        <button class="btn btn-outline-primary" title="{% trans 'Preview' %}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" title="{% trans 'Download' %}">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-info" title="{% trans 'Print' %}">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Certificate Templates -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card certificate-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-palette me-2"></i>{% trans "Certificate Templates" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="template-gallery">
                        <div class="template-item selected">
                            <i class="fas fa-award fa-2x text-warning mb-2"></i>
                            <h6>{% trans "Achievement Template" %}</h6>
                            <p class="small text-muted">{% trans "For academic achievements" %}</p>
                        </div>
                        <div class="template-item">
                            <i class="fas fa-graduation-cap fa-2x text-primary mb-2"></i>
                            <h6>{% trans "Completion Template" %}</h6>
                            <p class="small text-muted">{% trans "For course completion" %}</p>
                        </div>
                        <div class="template-item">
                            <i class="fas fa-medal fa-2x text-success mb-2"></i>
                            <h6>{% trans "Honor Template" %}</h6>
                            <p class="small text-muted">{% trans "For honor roll students" %}</p>
                        </div>
                        <div class="template-item">
                            <i class="fas fa-handshake fa-2x text-info mb-2"></i>
                            <h6>{% trans "Participation Template" %}</h6>
                            <p class="small text-muted">{% trans "For event participation" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Certificate Modal -->
<div class="modal fade" id="newCertificateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Create New Certificate" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="certificateStudent" class="form-label">{% trans "Student" %}</label>
                                <select class="form-select" id="certificateStudent">
                                    <option value="">{% trans "Select student..." %}</option>
                                    <option value="1">أحمد محمد علي - STU001</option>
                                    <option value="2">فاطمة أحمد حسن - STU002</option>
                                    <option value="3">محمد عبدالله سالم - STU003</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="certificateType" class="form-label">{% trans "Certificate Type" %}</label>
                                <select class="form-select" id="certificateType">
                                    <option value="achievement">{% trans "Achievement Award" %}</option>
                                    <option value="completion">{% trans "Course Completion" %}</option>
                                    <option value="honor">{% trans "Honor Roll" %}</option>
                                    <option value="participation">{% trans "Participation" %}</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="certificateTitle" class="form-label">{% trans "Certificate Title" %}</label>
                                <input type="text" class="form-control" id="certificateTitle" placeholder="{% trans 'Enter certificate title' %}">
                            </div>
                            <div class="mb-3">
                                <label for="certificateDescription" class="form-label">{% trans "Description" %}</label>
                                <textarea class="form-control" id="certificateDescription" rows="3" placeholder="{% trans 'Enter certificate description' %}"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="issueDate" class="form-label">{% trans "Issue Date" %}</label>
                                <input type="date" class="form-control" id="issueDate">
                            </div>
                            <div class="mb-3">
                                <label for="template" class="form-label">{% trans "Template" %}</label>
                                <select class="form-select" id="template">
                                    <option value="achievement">{% trans "Achievement Template" %}</option>
                                    <option value="completion">{% trans "Completion Template" %}</option>
                                    <option value="honor">{% trans "Honor Template" %}</option>
                                    <option value="participation">{% trans "Participation Template" %}</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="signatory" class="form-label">{% trans "Signatory" %}</label>
                                <select class="form-select" id="signatory">
                                    <option value="principal">{% trans "Principal" %}</option>
                                    <option value="vice_principal">{% trans "Vice Principal" %}</option>
                                    <option value="teacher">{% trans "Class Teacher" %}</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoIssue">
                                    <label class="form-check-label" for="autoIssue">
                                        {% trans "Issue immediately" %}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Generate Certificate" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    document.getElementById('issueDate').value = new Date().toISOString().split('T')[0];

    // Template selection
    document.querySelectorAll('.template-item').forEach(item => {
        item.addEventListener('click', function() {
            document.querySelectorAll('.template-item').forEach(t => t.classList.remove('selected'));
            this.classList.add('selected');
        });
    });

    // Handle action buttons
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Preview certificate functionality would be implemented here" %}');
        });
    });

    document.querySelectorAll('.btn-outline-success').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Download certificate functionality would be implemented here" %}');
        });
    });

    document.querySelectorAll('.btn-outline-info').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Print certificate functionality would be implemented here" %}');
        });
    });

    document.querySelectorAll('.btn-outline-warning').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Edit certificate functionality would be implemented here" %}');
        });
    });
});
</script>
{% endblock %}
