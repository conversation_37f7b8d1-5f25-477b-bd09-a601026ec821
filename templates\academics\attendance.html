{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load dashboard_tags %}

{% block title %}{% trans "Attendance Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .attendance-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .attendance-card:hover {
        transform: translateY(-2px);
    }
    .attendance-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .status-present { background-color: #d4edda; color: #155724; }
    .status-absent { background-color: #f8d7da; color: #721c24; }
    .status-late { background-color: #fff3cd; color: #856404; }
    .status-excused { background-color: #d1ecf1; color: #0c5460; }
    .student-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    .attendance-rate {
        font-size: 1.2em;
        font-weight: bold;
    }
    .calendar-day {
        border: 1px solid #dee2e6;
        padding: 10px;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    .calendar-day:hover {
        background-color: #f8f9fa;
    }
    .calendar-day.selected {
        background-color: #007bff;
        color: white;
    }
    .calendar-day.has-attendance {
        border-left: 4px solid #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-check me-2"></i>{% trans "Attendance Management" %}
                    </h1>
                    <p class="text-muted">{% trans "Track student attendance and participation" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:attendance_create' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "Take Attendance" %}
                    </a>
                    <a href="{% url 'academics:attendance_report' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Attendance Report" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_students|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ present_today|default:0 }}</h4>
                    <p class="mb-0">{% trans "Present Today" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">{{ absent_today|default:0 }}</h4>
                    <p class="mb-0">{% trans "Absent Today" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-percentage text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">{{ attendance_rate|floatformat:1|default:"0.0" }}%</h4>
                    <p class="mb-0">{% trans "Attendance Rate" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="class_filter" class="form-label">{% trans "Class" %}</label>
                            <select name="class" id="class_filter" class="form-select">
                                <option value="">{% trans "All Classes" %}</option>
                                {% for class in classes %}
                                    <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                                        {{ class.grade.name }} - {{ class.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_filter" class="form-label">{% trans "Date" %}</label>
                            <input type="date" name="date" id="date_filter" class="form-control" value="{{ selected_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="status_filter" class="form-label">{% trans "Status" %}</label>
                            <select name="status" id="status_filter" class="form-select">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="present" {% if selected_status == 'present' %}selected{% endif %}>{% trans "Present" %}</option>
                                <option value="absent" {% if selected_status == 'absent' %}selected{% endif %}>{% trans "Absent" %}</option>
                                <option value="late" {% if selected_status == 'late' %}selected{% endif %}>{% trans "Late" %}</option>
                                <option value="excused" {% if selected_status == 'excused' %}selected{% endif %}>{% trans "Excused" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                                </button>
                                <a href="{% url 'academics:attendance' %}" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-times me-2"></i>{% trans "Clear" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Records -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Attendance Records" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Class" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Time In" %}</th>
                                    <th>{% trans "Time Out" %}</th>
                                    <th>{% trans "Notes" %}</th>
                                    <th>{% trans "Recorded By" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if attendance_records %}
                                    {% for record in attendance_records %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {% if record.student.user.profile_picture %}
                                                        <img src="{{ record.student.user.profile_picture.url }}" alt="{{ record.student.user.get_full_name }}" class="student-avatar me-2">
                                                    {% else %}
                                                        <div class="student-avatar bg-secondary d-flex align-items-center justify-content-center me-2">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ record.student.user.first_name }} {{ record.student.user.last_name }}</div>
                                                        <small class="text-muted">{{ record.student.student_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="fw-bold">{{ record.class_name.grade.name }} - {{ record.class_name.name }}</div>
                                                    <small class="text-muted">{{ record.class_name.students.count }} {% trans "students" %}</small>
                                                </div>
                                            </td>
                                            <td>{{ record.date|date:"M d, Y" }}</td>
                                            <td>
                                                <span class="badge 
                                                    {% if record.status == 'present' %}bg-success
                                                    {% elif record.status == 'absent' %}bg-danger
                                                    {% elif record.status == 'late' %}bg-warning
                                                    {% elif record.status == 'excused' %}bg-info
                                                    {% else %}bg-secondary{% endif %}">
                                                    {% if record.status == 'present' %}{% trans "Present" %}
                                                    {% elif record.status == 'absent' %}{% trans "Absent" %}
                                                    {% elif record.status == 'late' %}{% trans "Late" %}
                                                    {% elif record.status == 'excused' %}{% trans "Excused" %}
                                                    {% else %}{{ record.status }}{% endif %}
                                                </span>
                                            </td>
                                            <td>
                                                {% if record.time_in %}
                                                    {{ record.time_in|time:"H:i" }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.time_out %}
                                                    {{ record.time_out|time:"H:i" }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.notes %}
                                                    <span class="text-truncate" style="max-width: 150px;" title="{{ record.notes }}">
                                                        {{ record.notes|truncatechars:30 }}
                                                    </span>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if record.recorded_by %}
                                                    {{ record.recorded_by.first_name }} {{ record.recorded_by.last_name }}
                                                {% else %}
                                                    <span class="text-muted">{% trans "System" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'academics:attendance_detail' record.id %}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'academics:attendance_update' record.id %}" class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                {% trans "No attendance records found. Take attendance to track student participation." %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('#class_filter, #date_filter, #status_filter').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}
