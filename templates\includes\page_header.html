{% load i18n %}
{% load navigation_tags %}

<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    {% if page_icon %}<i class="{{ page_icon }} me-3"></i>{% endif %}
                    {{ page_title|default:"Page Title" }}
                </h1>
                {% if page_description %}
                    <p class="page-description">{{ page_description }}</p>
                {% endif %}
            </div>
            <div class="col-md-4 text-end">
                {% if page_actions %}
                    <div class="page-actions">
                        {{ page_actions }}
                    </div>
                {% endif %}
            </div>
        </div>
        
        {% if page_stats %}
            <div class="row mt-3">
                <div class="col-12">
                    <div class="page-stats">
                        {{ page_stats }}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<style>
.page-header .page-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.page-stat {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 10px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    display: block;
}

.page-stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-top: 0.25rem;
}

.page-actions .btn {
    margin-left: 0.5rem;
}

@media (max-width: 768px) {
    .page-header .col-md-4 {
        text-align: center !important;
        margin-top: 1rem;
    }
    
    .page-stats {
        justify-content: center;
    }
    
    .page-stat {
        flex: 1;
        min-width: 120px;
    }
}
</style>
