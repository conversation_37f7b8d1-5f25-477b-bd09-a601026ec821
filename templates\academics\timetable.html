{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Class Timetable" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .timetable-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .timetable-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
        text-align: center;
    }
    .time-slot {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 0.75rem;
        text-align: center;
        font-weight: 600;
        color: #495057;
    }
    .subject-cell {
        border: 1px solid #dee2e6;
        padding: 0.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        min-height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .subject-cell:hover {
        background-color: #f8f9fa;
        transform: scale(1.02);
    }
    .subject-math {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .subject-arabic {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
    }
    .subject-science {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
    }
    .subject-english {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        color: white;
    }
    .subject-art {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    .subject-pe {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #495057;
    }
    .break-cell {
        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
        color: #2d3436;
        font-weight: bold;
    }
    .empty-cell {
        background: #f8f9fa;
        color: #6c757d;
        font-style: italic;
    }
    .teacher-name {
        font-size: 0.75rem;
        opacity: 0.9;
        margin-top: 0.25rem;
    }
    .room-number {
        font-size: 0.7rem;
        opacity: 0.8;
    }
    .class-selector {
        background: white;
        border-radius: 15px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }
    .legend {
        background: white;
        border-radius: 15px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        margin-right: 0.5rem;
    }
    .timetable-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .day-header {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        font-weight: bold;
        padding: 1rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-calendar-week text-primary me-2"></i>{% trans "Class Timetable" %}
                    </h2>
                    <p class="text-muted">{% trans "Weekly class schedule and subject allocation" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#editScheduleModal">
                        <i class="fas fa-edit me-2"></i>{% trans "Edit Schedule" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-print me-2"></i>{% trans "Print Timetable" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Selector -->
    <div class="class-selector">
        <div class="row align-items-center">
            <div class="col-md-3">
                <label for="classSelect" class="form-label fw-bold">{% trans "Select Class" %}</label>
                <select class="form-select" id="classSelect" onchange="window.location.href='?class=' + this.value">
                    {% for class in classes %}
                        <option value="{{ class.id }}" {% if selected_class and class.id == selected_class.id %}selected{% endif %}>
                            {{ class.grade.name }} - {{ class.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="weekSelect" class="form-label fw-bold">{% trans "Select Week" %}</label>
                <select class="form-select" id="weekSelect">
                    <option value="current">{% trans "Current Week" %}</option>
                    <option value="next">{% trans "Next Week" %}</option>
                    <option value="custom">{% trans "Custom Date" %}</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label fw-bold">{% trans "Current Academic Year" %}</label>
                <div class="fw-bold text-primary">2024-2025</div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>{% trans "Refresh" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Timetable -->
        <div class="col-lg-9 mb-4">
            <div class="card timetable-card">
                <div class="timetable-header">
                    <h4 class="mb-1">
                        {% if selected_class %}
                            {{ selected_class.grade.name }} - {{ selected_class.name }} - {% trans "Weekly Timetable" %}
                        {% else %}
                            {% trans "Weekly Timetable" %}
                        {% endif %}
                    </h4>
                    <p class="mb-0">{% trans "Week of July 14-18, 2024" %}</p>
                </div>
                <div class="card-body p-0">
                    <table class="timetable-table">
                        <thead>
                            <tr>
                                <th class="time-slot">{% trans "Time" %}</th>
                                <th class="day-header">{% trans "Sunday" %}</th>
                                <th class="day-header">{% trans "Monday" %}</th>
                                <th class="day-header">{% trans "Tuesday" %}</th>
                                <th class="day-header">{% trans "Wednesday" %}</th>
                                <th class="day-header">{% trans "Thursday" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if schedule_by_day %}
                                {% for time_slot in "08:00-08:45,08:45-09:30,09:30-10:00,10:00-10:45,10:45-11:30,11:30-12:00,12:00-12:45"|split:"," %}
                                    <tr>
                                        <td class="time-slot">{{ time_slot }}</td>

                                        {% if time_slot == "09:30-10:00" or time_slot == "11:30-12:00" %}
                                            <td class="subject-cell break-cell" colspan="5">
                                                <div>
                                                    <i class="fas fa-{% if time_slot == '09:30-10:00' %}coffee{% else %}utensils{% endif %} me-2"></i>
                                                    {% if time_slot == "09:30-10:00" %}
                                                        {% trans "Break Time" %}
                                                    {% else %}
                                                        {% trans "Lunch Break" %}
                                                    {% endif %}
                                                </div>
                                            </td>
                                        {% else %}
                                            {% for day in days %}
                                                {% with day_schedule=schedule_by_day|get:day %}
                                                    {% with schedule_item=day_schedule|filter_by_time:time_slot %}
                                                        {% if schedule_item %}
                                                            <td class="subject-cell subject-{% if schedule_item.class_subject.subject.name == 'Mathematics' %}math{% elif schedule_item.class_subject.subject.name == 'Arabic' %}arabic{% elif schedule_item.class_subject.subject.name == 'Science' %}science{% elif schedule_item.class_subject.subject.name == 'English' %}english{% elif schedule_item.class_subject.subject.name == 'Art' %}art{% elif schedule_item.class_subject.subject.name == 'Physical Education' %}pe{% else %}other{% endif %}">
                                                                <div>{{ schedule_item.class_subject.subject.name }}</div>
                                                                <div class="teacher-name">
                                                                    {% if schedule_item.class_subject.teacher %}
                                                                        {{ schedule_item.class_subject.teacher.user.first_name }} {{ schedule_item.class_subject.teacher.user.last_name }}
                                                                    {% else %}
                                                                        {% trans "Not assigned" %}
                                                                    {% endif %}
                                                                </div>
                                                                <div class="room-number">
                                                                    {% if schedule_item.room %}
                                                                        {% trans "Room" %} {{ schedule_item.room }}
                                                                    {% else %}
                                                                        {% trans "No room assigned" %}
                                                                    {% endif %}
                                                                </div>
                                                            </td>
                                                        {% else %}
                                                            <td class="subject-cell empty-cell">
                                                                <div>{% trans "Free Period" %}</div>
                                                            </td>
                                                        {% endif %}
                                                    {% endwith %}
                                                {% endwith %}
                                            {% endfor %}
                                        {% endif %}
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            {% trans "No schedule available for this class. Please select a different class or create a schedule." %}
                                        </div>
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Legend and Info -->
        <div class="col-lg-3">
            <!-- Subject Legend -->
            <div class="legend mb-4">
                <h5 class="mb-3">
                    <i class="fas fa-palette me-2"></i>{% trans "Subject Legend" %}
                </h5>
                <div class="legend-item">
                    <div class="legend-color subject-math"></div>
                    <span>{% trans "Mathematics" %}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color subject-arabic"></div>
                    <span>{% trans "Arabic Language" %}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color subject-science"></div>
                    <span>{% trans "Science" %}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color subject-english"></div>
                    <span>{% trans "English Language" %}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color subject-art"></div>
                    <span>{% trans "Art" %}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color subject-pe"></div>
                    <span>{% trans "Physical Education" %}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color break-cell"></div>
                    <span>{% trans "Break Time" %}</span>
                </div>
            </div>

            <!-- Class Information -->
            <div class="legend">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>{% trans "Class Information" %}
                </h5>
                <div class="mb-2">
                    <strong>{% trans "Class Teacher:" %}</strong><br>
                    {% if selected_class.teacher %}
                        {{ selected_class.teacher.user.first_name }} {{ selected_class.teacher.user.last_name }}
                    {% else %}
                        {% trans "Not assigned" %}
                    {% endif %}
                </div>
                <div class="mb-2">
                    <strong>{% trans "Total Students:" %}</strong><br>
                    {{ student_count|default:0 }} {% trans "students" %}
                </div>
                <div class="mb-2">
                    <strong>{% trans "Weekly Hours:" %}</strong><br>
                    {{ total_weekly_hours|default:0 }} {% trans "hours" %}
                </div>
                <div class="mb-2">
                    <strong>{% trans "Classroom:" %}</strong><br>
                    {% if selected_class.room_number %}
                        {% trans "Room" %} {{ selected_class.room_number }}
                    {% else %}
                        {% trans "Not assigned" %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Schedule Modal -->
<div class="modal fade" id="editScheduleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Edit Class Schedule" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Click on any time slot in the timetable to edit the subject assignment." %}
                </div>
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="editDay" class="form-label">{% trans "Day" %}</label>
                            <select class="form-select" id="editDay">
                                <option value="sunday">{% trans "Sunday" %}</option>
                                <option value="monday">{% trans "Monday" %}</option>
                                <option value="tuesday">{% trans "Tuesday" %}</option>
                                <option value="wednesday">{% trans "Wednesday" %}</option>
                                <option value="thursday">{% trans "Thursday" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editTime" class="form-label">{% trans "Time Slot" %}</label>
                            <select class="form-select" id="editTime">
                                <option value="08:00-08:45">08:00 - 08:45</option>
                                <option value="08:45-09:30">08:45 - 09:30</option>
                                <option value="10:00-10:45">10:00 - 10:45</option>
                                <option value="10:45-11:30">10:45 - 11:30</option>
                                <option value="12:00-12:45">12:00 - 12:45</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editSubject" class="form-label">{% trans "Subject" %}</label>
                            <select class="form-select" id="editSubject">
                                <option value="">{% trans "Select Subject" %}</option>
                                <option value="math">{% trans "Mathematics" %}</option>
                                <option value="arabic">{% trans "Arabic Language" %}</option>
                                <option value="science">{% trans "Science" %}</option>
                                <option value="english">{% trans "English Language" %}</option>
                                <option value="art">{% trans "Art" %}</option>
                                <option value="pe">{% trans "Physical Education" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editTeacher" class="form-label">{% trans "Teacher" %}</label>
                            <select class="form-select" id="editTeacher">
                                <option value="">{% trans "Select Teacher" %}</option>
                                <option value="1">أحمد محمد علي</option>
                                <option value="2">فاطمة أحمد حسن</option>
                                <option value="3">محمد عبدالله سالم</option>
                                <option value="4">سارة محمد أحمد</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editRoom" class="form-label">{% trans "Room" %}</label>
                            <input type="text" class="form-control" id="editRoom" placeholder="{% trans 'Room number' %}">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Save Changes" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Class selector change
    const classSelect = document.getElementById('classSelect');
    classSelect.addEventListener('change', function() {
        // Here you would load the timetable for the selected class
        console.log('Loading timetable for class:', this.value);
    });

    // Week selector change
    const weekSelect = document.getElementById('weekSelect');
    weekSelect.addEventListener('change', function() {
        // Here you would load the timetable for the selected week
        console.log('Loading timetable for week:', this.value);
    });

    // Make timetable cells clickable for editing
    document.querySelectorAll('.subject-cell:not(.break-cell)').forEach(cell => {
        cell.addEventListener('click', function() {
            // Open edit modal with current cell data
            const editModal = new bootstrap.Modal(document.getElementById('editScheduleModal'));
            editModal.show();
        });
    });

    // Print functionality
    document.querySelector('.btn-success').addEventListener('click', function() {
        window.print();
    });
});
</script>
{% endblock %}
