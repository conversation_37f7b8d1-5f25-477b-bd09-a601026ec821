from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel, AcademicYear, Semester
from students.models import Grade, Class, Student


class Subject(BaseModel):
    """
    Subject model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Subject Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Subject Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Subject Code')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    grades = models.ManyToManyField(
        Grade,
        related_name='subjects',
        verbose_name=_('Grades')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    credit_hours = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Credit Hours')
    )

    class Meta:
        verbose_name = _('Subject')
        verbose_name_plural = _('Subjects')
        ordering = ['name']

    def __str__(self):
        return self.name


class Teacher(BaseModel):
    """
    Teacher model
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='teacher_profile',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('User Account')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Employee ID')
    )

    subjects = models.ManyToManyField(
        Subject,
        related_name='teachers',
        verbose_name=_('Subjects')
    )

    hire_date = models.DateField(
        verbose_name=_('Hire Date')
    )

    qualification = models.CharField(
        max_length=200,
        verbose_name=_('Qualification')
    )

    experience_years = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Experience Years')
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Salary')
    )

    department = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Department')
    )

    class Meta:
        verbose_name = _('Teacher')
        verbose_name_plural = _('Teachers')
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} ({self.employee_id})"


class ClassSubject(BaseModel):
    """
    Class-Subject assignment model
    """
    class_obj = models.ForeignKey(
        Class,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Class')
    )

    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Subject')
    )

    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Teacher')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Academic Year')
    )

    weekly_hours = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Weekly Hours')
    )

    class Meta:
        verbose_name = _('Class Subject')
        verbose_name_plural = _('Class Subjects')
        unique_together = ['class_obj', 'subject', 'academic_year']
        ordering = ['class_obj', 'subject']

    def __str__(self):
        return f"{self.class_obj} - {self.subject} ({self.teacher})"


class Schedule(BaseModel):
    """
    Class schedule model
    """
    DAYS_OF_WEEK = (
        ('monday', _('Monday')),
        ('tuesday', _('Tuesday')),
        ('wednesday', _('Wednesday')),
        ('thursday', _('Thursday')),
        ('friday', _('Friday')),
        ('saturday', _('Saturday')),
        ('sunday', _('Sunday')),
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('Class Subject')
    )

    day_of_week = models.CharField(
        max_length=10,
        choices=DAYS_OF_WEEK,
        verbose_name=_('Day of Week')
    )

    start_time = models.TimeField(
        verbose_name=_('Start Time')
    )

    end_time = models.TimeField(
        verbose_name=_('End Time')
    )

    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Room Number')
    )

    class Meta:
        verbose_name = _('Schedule')
        verbose_name_plural = _('Schedules')
        ordering = ['day_of_week', 'start_time']

    def __str__(self):
        return f"{self.class_subject} - {self.get_day_of_week_display()} {self.start_time}"


class Exam(BaseModel):
    """
    Exam model
    """
    EXAM_TYPES = (
        ('quiz', _('Quiz')),
        ('midterm', _('Midterm')),
        ('final', _('Final')),
        ('assignment', _('Assignment')),
        ('project', _('Project')),
        ('oral', _('Oral Exam')),
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Exam Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Exam Name (Arabic)')
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='exams',
        verbose_name=_('Class Subject')
    )

    exam_type = models.CharField(
        max_length=20,
        choices=EXAM_TYPES,
        verbose_name=_('Exam Type')
    )

    exam_date = models.DateField(
        verbose_name=_('Exam Date')
    )

    start_time = models.TimeField(
        verbose_name=_('Start Time')
    )

    end_time = models.TimeField(
        verbose_name=_('End Time')
    )

    duration_minutes = models.PositiveIntegerField(
        verbose_name=_('Duration (Minutes)')
    )

    total_marks = models.PositiveIntegerField(
        verbose_name=_('Total Marks')
    )

    passing_marks = models.PositiveIntegerField(
        verbose_name=_('Passing Marks')
    )

    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Room Number')
    )

    instructions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Instructions')
    )

    is_published = models.BooleanField(
        default=False,
        verbose_name=_('Is Published')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_exams',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Exam')
        verbose_name_plural = _('Exams')
        ordering = ['-exam_date', '-start_time']

    def __str__(self):
        return f"{self.name} - {self.class_subject}"

    @property
    def is_completed(self):
        from datetime import datetime, date
        exam_datetime = datetime.combine(self.exam_date, self.end_time)
        return exam_datetime < datetime.now()


class StudentGrade(BaseModel):
    """
    Student grade/assessment model
    """
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='academic_grades',
        verbose_name=_('Student')
    )

    exam = models.ForeignKey(
        Exam,
        on_delete=models.CASCADE,
        related_name='student_grades',
        verbose_name=_('Exam')
    )

    marks_obtained = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Marks Obtained')
    )

    grade_letter = models.CharField(
        max_length=2,
        blank=True,
        null=True,
        verbose_name=_('Grade Letter')
    )

    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Percentage')
    )

    remarks = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Remarks')
    )

    graded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='graded_assessments',
        verbose_name=_('Graded By')
    )

    graded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Graded At')
    )

    class Meta:
        verbose_name = _('Student Grade')
        verbose_name_plural = _('Student Grades')
        unique_together = ['student', 'exam']
        ordering = ['-graded_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.exam.name} ({self.marks_obtained}/{self.exam.total_marks})"

    def save(self, *args, **kwargs):
        # Auto-calculate percentage
        if self.marks_obtained is not None and self.exam.total_marks > 0:
            self.percentage = (self.marks_obtained / self.exam.total_marks) * 100

            # Auto-assign grade letter based on percentage
            if self.percentage >= 90:
                self.grade_letter = 'A+'
            elif self.percentage >= 85:
                self.grade_letter = 'A'
            elif self.percentage >= 80:
                self.grade_letter = 'B+'
            elif self.percentage >= 75:
                self.grade_letter = 'B'
            elif self.percentage >= 70:
                self.grade_letter = 'C+'
            elif self.percentage >= 65:
                self.grade_letter = 'C'
            elif self.percentage >= 60:
                self.grade_letter = 'D'
            else:
                self.grade_letter = 'F'

        super().save(*args, **kwargs)


class StudentAttendance(BaseModel):
    """
    Student attendance model for academic classes
    """
    ATTENDANCE_STATUS = (
        ('present', _('Present')),
        ('absent', _('Absent')),
        ('late', _('Late')),
        ('excused', _('Excused')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='academic_attendance',
        verbose_name=_('Student')
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('Class Subject')
    )

    date = models.DateField(
        verbose_name=_('Date')
    )

    status = models.CharField(
        max_length=10,
        choices=ATTENDANCE_STATUS,
        verbose_name=_('Status')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    marked_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='marked_attendance',
        verbose_name=_('Marked By')
    )

    class Meta:
        verbose_name = _('Student Attendance')
        verbose_name_plural = _('Student Attendance')
        unique_together = ['student', 'class_subject', 'date']
        ordering = ['-date', 'student']

    def __str__(self):
        return f"{self.student.full_name} - {self.class_subject} ({self.date})"


class Curriculum(BaseModel):
    """
    Curriculum model for academic planning
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Curriculum Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Curriculum Name (Arabic)')
    )

    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='curriculums',
        verbose_name=_('Grade')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='curriculums',
        verbose_name=_('Academic Year')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    objectives = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Learning Objectives')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_curriculums',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Curriculum')
        verbose_name_plural = _('Curriculums')
        ordering = ['grade', 'name']

    def __str__(self):
        return f"{self.name} - {self.grade}"


class CurriculumSubject(BaseModel):
    """
    Curriculum subject mapping model
    """
    curriculum = models.ForeignKey(
        Curriculum,
        on_delete=models.CASCADE,
        related_name='curriculum_subjects',
        verbose_name=_('Curriculum')
    )

    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='curriculum_mappings',
        verbose_name=_('Subject')
    )

    weekly_hours = models.PositiveIntegerField(
        verbose_name=_('Weekly Hours')
    )

    semester = models.CharField(
        max_length=20,
        choices=[
            ('first', _('First Semester')),
            ('second', _('Second Semester')),
            ('full_year', _('Full Year')),
        ],
        default='full_year',
        verbose_name=_('Semester')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    learning_outcomes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Learning Outcomes')
    )

    assessment_criteria = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Assessment Criteria')
    )

    class Meta:
        verbose_name = _('Curriculum Subject')
        verbose_name_plural = _('Curriculum Subjects')
        unique_together = ['curriculum', 'subject']
        ordering = ['curriculum', 'subject']

    def __str__(self):
        return f"{self.curriculum.name} - {self.subject.name}"
