{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Class" %} - {{ object.name }}
    {% else %}
        {% trans "Add New Class" %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .btn-primary {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
    }
    .btn-secondary {
        border-radius: 10px;
        padding: 0.75rem 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">{% trans "Students" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:class_list' %}">{% trans "Classes" %}</a></li>
            <li class="breadcrumb-item active">
                {% if object %}
                    {% trans "Edit Class" %}
                {% else %}
                    {% trans "Add Class" %}
                {% endif %}
            </li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        {% if object %}
                            {% trans "Edit Class" %}: {{ object.name }}
                        {% else %}
                            {% trans "Add New Class" %}
                        {% endif %}
                    </h4>
                    <p class="mb-0 opacity-75">
                        {% if object %}
                            {% trans "Update class information and settings" %}
                        {% else %}
                            {% trans "Create a new class for students" %}
                        {% endif %}
                    </p>
                </div>
                
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="fas fa-users text-primary me-2"></i>{% trans "Class Name" %} *
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.grade.id_for_label }}" class="form-label">
                                    <i class="fas fa-graduation-cap text-primary me-2"></i>{% trans "Grade Level" %} *
                                </label>
                                {{ form.grade }}
                                {% if form.grade.errors %}
                                    <div class="text-danger small mt-1">{{ form.grade.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.capacity.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-friends text-primary me-2"></i>{% trans "Capacity" %}
                                </label>
                                {{ form.capacity }}
                                {% if form.capacity.errors %}
                                    <div class="text-danger small mt-1">{{ form.capacity.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{% trans "Maximum number of students in this class" %}</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.room_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-door-open text-primary me-2"></i>{% trans "Room Number" %}
                                </label>
                                {{ form.room_number }}
                                {% if form.room_number.errors %}
                                    <div class="text-danger small mt-1">{{ form.room_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left text-primary me-2"></i>{% trans "Description" %}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <i class="fas fa-toggle-on text-success me-2"></i>{% trans "Active Class" %}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small mt-1">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'students:class_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}
                                    {% trans "Update Class" %}
                                {% else %}
                                    {% trans "Create Class" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(control) {
        if (!control.classList.contains('form-check-input')) {
            control.classList.add('form-control');
        }
    });
    
    // Auto-suggest room number based on class name
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    const roomField = document.getElementById('{{ form.room_number.id_for_label }}');
    
    if (nameField && roomField && !roomField.value) {
        nameField.addEventListener('input', function() {
            const name = this.value.trim();
            if (name) {
                // Extract numbers from class name for room suggestion
                const numbers = name.match(/\d+/);
                if (numbers) {
                    roomField.value = 'Room ' + numbers[0];
                }
            }
        });
    }
    
    // Capacity validation
    const capacityField = document.getElementById('{{ form.capacity.id_for_label }}');
    if (capacityField) {
        capacityField.addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value && value > 50) {
                this.setCustomValidity('{% trans "Capacity should not exceed 50 students" %}');
            } else {
                this.setCustomValidity('');
            }
        });
    }
});
</script>
{% endblock %}
