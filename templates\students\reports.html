{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Student Reports" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
        cursor: pointer;
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    .report-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    .report-category {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1rem;
    }
    .stats-widget {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
    .chart-container {
        position: relative;
        height: 300px;
        margin: 1rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-bar text-primary me-2"></i>{% trans "Student Reports" %}
                    </h2>
                    <p class="text-muted">{% trans "Generate comprehensive reports and analytics for student data" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "Custom Report" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-download me-2"></i>{% trans "Export All" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3 class="mb-1">1,247</h3>
                <p class="mb-0">{% trans "Total Students" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                <h3 class="mb-1">156</h3>
                <p class="mb-0">{% trans "Graduates This Year" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h3 class="mb-1">92.5%</h3>
                <p class="mb-0">{% trans "Average Attendance" %}</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-widget">
                <i class="fas fa-star fa-2x mb-2"></i>
                <h3 class="mb-1">4.2</h3>
                <p class="mb-0">{% trans "Average Grade" %}</p>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="row">
        <!-- Academic Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="report-category">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>{% trans "Academic Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            {% trans "Grade Analysis" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-trophy text-warning me-2"></i>
                            {% trans "Top Performers" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                            {% trans "At-Risk Students" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-clipboard-list text-info me-2"></i>
                            {% trans "Subject Performance" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="report-category">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>{% trans "Attendance Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-day text-success me-2"></i>
                            {% trans "Daily Attendance" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-week text-primary me-2"></i>
                            {% trans "Weekly Summary" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-alt text-info me-2"></i>
                            {% trans "Monthly Report" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-times text-danger me-2"></i>
                            {% trans "Absentee Report" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demographic Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="report-category">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>{% trans "Demographic Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-venus-mars text-purple me-2"></i>
                            {% trans "Gender Distribution" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-birthday-cake text-pink me-2"></i>
                            {% trans "Age Groups" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-map-marker-alt text-danger me-2"></i>
                            {% trans "Geographic Distribution" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-school text-primary me-2"></i>
                            {% trans "Class Distribution" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="report-category">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>{% trans "Financial Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill text-success me-2"></i>
                            {% trans "Fee Collection" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-exclamation-circle text-warning me-2"></i>
                            {% trans "Outstanding Dues" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-pie text-info me-2"></i>
                            {% trans "Payment Methods" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-receipt text-primary me-2"></i>
                            {% trans "Revenue Analysis" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Behavioral Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="report-category">
                    <h5 class="mb-0">
                        <i class="fas fa-user-check me-2"></i>{% trans "Behavioral Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-medal text-warning me-2"></i>
                            {% trans "Disciplinary Actions" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-star text-success me-2"></i>
                            {% trans "Merit Points" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-handshake text-primary me-2"></i>
                            {% trans "Conduct Reports" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-clipboard-check text-info me-2"></i>
                            {% trans "Incident Reports" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="report-category">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>{% trans "Custom Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus text-primary me-2"></i>
                            {% trans "Create New Report" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-save text-success me-2"></i>
                            {% trans "Saved Reports" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-clock text-warning me-2"></i>
                            {% trans "Scheduled Reports" %}
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-share text-info me-2"></i>
                            {% trans "Shared Reports" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>{% trans "Recent Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Report Name" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Generated" %}</th>
                                    <th>{% trans "Generated By" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{% trans "Monthly Attendance Summary" %}</td>
                                    <td><span class="badge bg-primary">{% trans "Attendance" %}</span></td>
                                    <td>2025-01-13 10:30 AM</td>
                                    <td>أحمد محمد</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Download' %}">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="{% trans 'Share' %}">
                                                <i class="fas fa-share"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{% trans "Grade Analysis Report" %}</td>
                                    <td><span class="badge bg-success">{% trans "Academic" %}</span></td>
                                    <td>2025-01-12 02:15 PM</td>
                                    <td>فاطمة أحمد</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Download' %}">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="{% trans 'Share' %}">
                                                <i class="fas fa-share"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{% trans "Fee Collection Report" %}</td>
                                    <td><span class="badge bg-warning">{% trans "Financial" %}</span></td>
                                    <td>2025-01-11 09:45 AM</td>
                                    <td>محمد عبدالله</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Download' %}">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="{% trans 'Share' %}">
                                                <i class="fas fa-share"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle report card clicks
    document.querySelectorAll('.report-card .list-group-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const reportName = this.textContent.trim();
            alert(`{% trans "Generating report:" %} ${reportName}`);
        });
    });

    // Handle action buttons
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "View report functionality would be implemented here" %}');
        });
    });

    document.querySelectorAll('.btn-outline-success').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Download report functionality would be implemented here" %}');
        });
    });

    document.querySelectorAll('.btn-outline-info').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Share report functionality would be implemented here" %}');
        });
    });
});
</script>
{% endblock %}
