/* School ERP Custom Styles */

:root {
    --primary-color: #4c63d2;
    --secondary-color: #6c5ce7;
    --success-color: #00b894;
    --danger-color: #e17055;
    --warning-color: #fdcb6e;
    --info-color: #0984e3;
    --light-color: #f8f9fa;
    --dark-color: #2d3436;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --text-color: #2d3436;
    --text-muted: #636e72;
    --border-color: #ddd;
    --focus-color: #4c63d2;
    --hover-bg: #f1f2f6;
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    line-height: 1.6;
    color: var(--text-color);
    font-size: 16px;
}

/* Arabic Font Support */
[lang="ar"], [dir="rtl"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    border-bottom: 3px solid var(--secondary-color);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 5px;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    padding: 0.75rem 1rem;
    font-size: 16px;
    color: var(--text-color);
    background-color: #ffffff;
}

.form-control:focus {
    border-color: var(--focus-color);
    box-shadow: 0 0 0 0.25rem rgba(76, 99, 210, 0.25);
    outline: none;
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
}

.form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 14px;
}

.form-text {
    color: var(--text-muted);
    font-size: 13px;
}

/* Enhanced form validation styles */
.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.25rem rgba(225, 112, 85, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.25rem rgba(0, 184, 148, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 500;
    font-size: 13px;
}

.valid-feedback {
    color: var(--success-color);
    font-weight: 500;
    font-size: 13px;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Badges */
.badge {
    border-radius: 20px;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

/* Loading Spinner */
.spinner-border {
    color: var(--primary-color);
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-custom {
    box-shadow: var(--box-shadow);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2c3e50;
        --dark-color: #ecf0f1;
    }
    
    body {
        background-color: #2c3e50;
        color: #ecf0f1;
    }
    
    .card {
        background-color: #34495e;
        color: #ecf0f1;
    }
    
    .form-control {
        background-color: #34495e;
        border-color: #4a5f7a;
        color: #ecf0f1;
    }
    
    .table {
        background-color: #34495e;
        color: #ecf0f1;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
}
