from django import template
from django.utils.translation import gettext as _
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.db.models import Count, Sum
from django.utils import timezone
from students.models import Student, Class
from academics.models import Teacher, Subject
from hr.models import Employee, Department
import json

register = template.Library()


@register.inclusion_tag('components/stat_card.html')
def stat_card(icon, number, label, color='primary', url=None):
    """
    Render a statistics card component
    """
    return {
        'icon': icon,
        'number': number,
        'label': label,
        'color': color,
        'url': url,
    }


@register.inclusion_tag('components/quick_action_btn.html')
def quick_action_btn(url, icon, label, color='primary'):
    """
    Render a quick action button component
    """
    return {
        'url': url,
        'icon': icon,
        'label': label,
        'color': color,
    }


@register.inclusion_tag('components/data_table.html')
def data_table(headers, rows, actions=None, table_id='data-table'):
    """
    Render a data table component
    """
    return {
        'headers': headers,
        'rows': rows,
        'actions': actions,
        'table_id': table_id,
    }


@register.inclusion_tag('components/filter_form.html')
def filter_form(form_fields, action_url='', method='get'):
    """
    Render a filter form component
    """
    return {
        'form_fields': form_fields,
        'action_url': action_url,
        'method': method,
    }


@register.inclusion_tag('components/pagination.html')
def pagination_nav(page_obj, request):
    """
    Render pagination navigation
    """
    return {
        'page_obj': page_obj,
        'request': request,
    }


@register.simple_tag
def get_student_stats():
    """
    Get student statistics
    """
    return {
        'total': Student.objects.filter(is_active=True).count(),
        'by_grade': Student.objects.filter(is_active=True).values(
            'current_class__grade__name'
        ).annotate(count=Count('id')),
        'new_this_month': Student.objects.filter(
            is_active=True,
            created_at__month=timezone.now().month
        ).count(),
    }


@register.simple_tag
def get_teacher_stats():
    """
    Get teacher statistics
    """
    return {
        'total': Teacher.objects.filter(is_active=True).count(),
        'by_subject': Teacher.objects.filter(is_active=True).values(
            'subjects__name'
        ).annotate(count=Count('id')),
    }


@register.simple_tag
def get_class_stats():
    """
    Get class statistics
    """
    return {
        'total': Class.objects.filter(is_active=True).count(),
        'by_grade': Class.objects.filter(is_active=True).values(
            'grade__name'
        ).annotate(count=Count('id')),
        'average_students': Class.objects.filter(is_active=True).annotate(
            student_count=Count('students')
        ).aggregate(avg=Sum('student_count'))['avg'] or 0,
    }


@register.filter
def get_item(dictionary, key):
    """
    Get item from dictionary by key
    """
    return dictionary.get(key)


@register.filter
def percentage(value, total):
    """
    Calculate percentage
    """
    if not total or total == 0:
        return 0
    return round((value / total) * 100, 1)


@register.filter
def mul(value, arg):
    """
    Multiply value by argument
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def div(value, arg):
    """
    Divide value by argument
    """
    try:
        if float(arg) == 0:
            return 0
        return float(value) / float(arg)
    except (ValueError, TypeError):
        return 0


@register.simple_tag
def user_can_access(user, permission):
    """
    Check if user has permission
    """
    return user.has_perm(permission)


@register.filter
def split(value, delimiter):
    """Split string by delimiter."""
    return value.split(delimiter)


@register.filter
def get(dictionary, key):
    """Get value from dictionary by key."""
    if hasattr(dictionary, 'get'):
        return dictionary.get(key)
    return None


@register.filter
def filter_by_time(schedule_list, time_slot):
    """Filter schedule items by time slot."""
    if not schedule_list:
        return None

    try:
        from datetime import datetime
        start_time_str, end_time_str = time_slot.split('-')
        start_time = datetime.strptime(start_time_str, '%H:%M').time()
        end_time = datetime.strptime(end_time_str, '%H:%M').time()

        for schedule in schedule_list:
            if schedule.start_time and schedule.end_time:
                if schedule.start_time <= start_time and schedule.end_time >= end_time:
                    return schedule
                # Also check if the schedule overlaps with the time slot
                if (schedule.start_time <= start_time < schedule.end_time or
                    schedule.start_time < end_time <= schedule.end_time):
                    return schedule
        return None
    except (ValueError, AttributeError):
        return None


@register.filter
def json_script(value, element_id=None):
    """
    Convert Python object to JSON for use in JavaScript.
    Similar to Django's json_script but as a filter.
    """
    json_str = json.dumps(value, ensure_ascii=False)
    if element_id:
        return mark_safe(f'<script id="{element_id}" type="application/json">{json_str}</script>')
    return mark_safe(json_str)


@register.inclusion_tag('components/breadcrumb.html')
def breadcrumb(items):
    """
    Render breadcrumb navigation
    """
    return {'items': items}


@register.inclusion_tag('components/alert.html')
def alert(message, alert_type='info', dismissible=True):
    """
    Render alert component
    """
    return {
        'message': message,
        'alert_type': alert_type,
        'dismissible': dismissible,
    }


@register.inclusion_tag('components/modal.html')
def modal(modal_id, title, body_content='', footer_content='', size=''):
    """
    Render modal component
    """
    return {
        'modal_id': modal_id,
        'title': title,
        'body_content': body_content,
        'footer_content': footer_content,
        'size': size,
    }
