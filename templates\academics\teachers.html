{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Teachers Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .teacher-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .teacher-card:hover {
        transform: translateY(-2px);
    }
    .teacher-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
    }
    .teacher-status {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
    }
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    .subject-badge {
        background-color: #e3f2fd;
        color: #1976d2;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        margin: 0.1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chalkboard-teacher text-primary me-2"></i>{% trans "Teachers Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage teachers, subjects, and academic assignments" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Add Teacher" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export List" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card teacher-card text-center">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h3 class="mb-1">{{ total_teachers|default:45 }}</h3>
                    <p class="mb-0">{% trans "Total Teachers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card teacher-card text-center">
                <div class="card-body">
                    <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                    <h3 class="mb-1">{{ active_teachers|default:42 }}</h3>
                    <p class="mb-0">{% trans "Active Teachers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card teacher-card text-center">
                <div class="card-body">
                    <i class="fas fa-book fa-2x text-info mb-2"></i>
                    <h3 class="mb-1">{{ total_subjects|default:28 }}</h3>
                    <p class="mb-0">{% trans "Subjects Taught" %}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card teacher-card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-alt fa-2x text-warning mb-2"></i>
                    <h3 class="mb-1">{{ classes_today|default:156 }}</h3>
                    <p class="mb-0">{% trans "Classes Today" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card teacher-card">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">{% trans "Search Teachers" %}</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="search" placeholder="{% trans 'Search by name, ID, or subject' %}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="department" class="form-label">{% trans "Department" %}</label>
                            <select class="form-select" id="department">
                                <option value="">{% trans "All Departments" %}</option>
                                <option value="science">{% trans "Science" %}</option>
                                <option value="math">{% trans "Mathematics" %}</option>
                                <option value="language">{% trans "Languages" %}</option>
                                <option value="social">{% trans "Social Studies" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="active">{% trans "Active" %}</option>
                                <option value="inactive">{% trans "Inactive" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="experience" class="form-label">{% trans "Experience" %}</label>
                            <select class="form-select" id="experience">
                                <option value="">{% trans "All Levels" %}</option>
                                <option value="0-2">0-2 {% trans "years" %}</option>
                                <option value="3-5">3-5 {% trans "years" %}</option>
                                <option value="6-10">6-10 {% trans "years" %}</option>
                                <option value="10+">10+ {% trans "years" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>{% trans "Filter" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Teachers List -->
    <div class="row">
        <div class="col-12">
            <div class="card teacher-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>{% trans "Teachers List" %}
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm active">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Teacher Card 1 -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <img src="{% static 'images/default-avatar.png' %}" alt="Teacher" class="teacher-avatar me-3">
                                        <div>
                                            <h6 class="mb-1">أحمد محمد علي</h6>
                                            <small class="text-muted">ID: TCH-001</small>
                                            <div>
                                                <span class="teacher-status status-active">{% trans "Active" %}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted d-block">{% trans "Subjects" %}:</small>
                                        <span class="subject-badge">{% trans "Mathematics" %}</span>
                                        <span class="subject-badge">{% trans "Physics" %}</span>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Experience" %}</small>
                                            <strong>8 {% trans "years" %}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Classes" %}</small>
                                            <strong>12</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Students" %}</small>
                                            <strong>285</strong>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info me-1">
                                            <i class="fas fa-calendar"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Card 2 -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <img src="{% static 'images/default-avatar.png' %}" alt="Teacher" class="teacher-avatar me-3">
                                        <div>
                                            <h6 class="mb-1">فاطمة أحمد حسن</h6>
                                            <small class="text-muted">ID: TCH-002</small>
                                            <div>
                                                <span class="teacher-status status-active">{% trans "Active" %}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted d-block">{% trans "Subjects" %}:</small>
                                        <span class="subject-badge">{% trans "Arabic" %}</span>
                                        <span class="subject-badge">{% trans "Literature" %}</span>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Experience" %}</small>
                                            <strong>12 {% trans "years" %}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Classes" %}</small>
                                            <strong>15</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Students" %}</small>
                                            <strong>320</strong>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info me-1">
                                            <i class="fas fa-calendar"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Card 3 -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <img src="{% static 'images/default-avatar.png' %}" alt="Teacher" class="teacher-avatar me-3">
                                        <div>
                                            <h6 class="mb-1">محمد عبدالله سالم</h6>
                                            <small class="text-muted">ID: TCH-003</small>
                                            <div>
                                                <span class="teacher-status status-inactive">{% trans "Inactive" %}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted d-block">{% trans "Subjects" %}:</small>
                                        <span class="subject-badge">{% trans "Chemistry" %}</span>
                                        <span class="subject-badge">{% trans "Biology" %}</span>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Experience" %}</small>
                                            <strong>5 {% trans "years" %}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Classes" %}</small>
                                            <strong>8</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">{% trans "Students" %}</small>
                                            <strong>180</strong>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info me-1">
                                            <i class="fas fa-calendar"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <span class="text-muted">{% trans "Showing 3 of 45 teachers" %}</span>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <span class="page-link">{% trans "Previous" %}</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">{% trans "Next" %}</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Search functionality
    document.getElementById('search').addEventListener('input', function() {
        // Implement search logic here
        console.log('Searching for:', this.value);
    });

    // Filter functionality
    document.querySelector('.btn-primary').addEventListener('click', function() {
        // Implement filter logic here
        console.log('Applying filters');
    });
</script>
{% endblock %}
