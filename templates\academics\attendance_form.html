{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Attendance" %} - {{ block.super }}
    {% else %}
        {% trans "Take Attendance" %} - {{ block.super }}
    {% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .attendance-form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .form-card-body {
        background: white;
        border-radius: 0 0 15px 15px;
        color: #333;
    }
    .form-header {
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
    }
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    .btn-secondary {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
    }
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        margin: 0.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .status-present {
        background-color: #d4edda;
        color: #155724;
        border: 2px solid transparent;
    }
    .status-present.selected {
        border-color: #28a745;
        background-color: #28a745;
        color: white;
    }
    .status-absent {
        background-color: #f8d7da;
        color: #721c24;
        border: 2px solid transparent;
    }
    .status-absent.selected {
        border-color: #dc3545;
        background-color: #dc3545;
        color: white;
    }
    .status-late {
        background-color: #fff3cd;
        color: #856404;
        border: 2px solid transparent;
    }
    .status-late.selected {
        border-color: #ffc107;
        background-color: #ffc107;
        color: #212529;
    }
    .status-excused {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 2px solid transparent;
    }
    .status-excused.selected {
        border-color: #17a2b8;
        background-color: #17a2b8;
        color: white;
    }
    .student-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-check me-2"></i>
                        {% if object %}
                            {% trans "Edit Attendance" %}
                        {% else %}
                            {% trans "Take Attendance" %}
                        {% endif %}
                    </h1>
                    <p class="text-muted">
                        {% if object %}
                            {% trans "Update student attendance record" %}
                        {% else %}
                            {% trans "Record student attendance for class" %}
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{% url 'academics:attendance' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Attendance" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card attendance-form-card">
                <div class="form-header">
                    <h4 class="mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        {% trans "Attendance Information" %}
                    </h4>
                </div>
                <div class="card-body form-card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Student Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.student.id_for_label }}" class="form-label required-field">
                                    {% trans "Student" %}
                                </label>
                                {{ form.student }}
                                {% if form.student.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.student.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Class Subject Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.class_subject.id_for_label }}" class="form-label required-field">
                                    {% trans "Class Subject" %}
                                </label>
                                {{ form.class_subject }}
                                {% if form.class_subject.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.class_subject.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- Date -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label required-field">
                                    {% trans "Date" %}
                                </label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.date.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label required-field">
                                    {% trans "Attendance Status" %}
                                </label>
                                <div class="d-flex flex-wrap">
                                    <div class="status-badge status-present" data-status="present">
                                        <i class="fas fa-check me-1"></i>{% trans "Present" %}
                                    </div>
                                    <div class="status-badge status-absent" data-status="absent">
                                        <i class="fas fa-times me-1"></i>{% trans "Absent" %}
                                    </div>
                                    <div class="status-badge status-late" data-status="late">
                                        <i class="fas fa-clock me-1"></i>{% trans "Late" %}
                                    </div>
                                    <div class="status-badge status-excused" data-status="excused">
                                        <i class="fas fa-user-shield me-1"></i>{% trans "Excused" %}
                                    </div>
                                </div>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="text-danger mt-1">
                                        {{ form.status.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-4">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {% trans "Notes" %}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {{ form.notes.errors }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans "Optional notes about the attendance record" %}
                            </small>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'academics:attendance' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}
                                    {% trans "Update Attendance" %}
                                {% else %}
                                    {% trans "Record Attendance" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status selection handling
    const statusBadges = document.querySelectorAll('.status-badge');
    const statusInput = document.getElementById('{{ form.status.id_for_label }}');

    // Hide the actual select field
    if (statusInput) {
        statusInput.style.display = 'none';
    }

    // Set initial selection
    const currentValue = statusInput ? statusInput.value : '';
    if (currentValue) {
        statusBadges.forEach(badge => {
            if (badge.dataset.status === currentValue) {
                badge.classList.add('selected');
            }
        });
    }

    // Handle status badge clicks
    statusBadges.forEach(badge => {
        badge.addEventListener('click', function() {
            // Remove selected class from all badges
            statusBadges.forEach(b => b.classList.remove('selected'));

            // Add selected class to clicked badge
            this.classList.add('selected');

            // Update hidden select field
            if (statusInput) {
                statusInput.value = this.dataset.status;
            }
        });
    });

    // Set today's date as default if creating new record
    const dateInput = document.getElementById('{{ form.date.id_for_label }}');
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;

        // Check required fields
        const requiredFields = ['{{ form.student.id_for_label }}', '{{ form.class_subject.id_for_label }}', '{{ form.date.id_for_label }}'];

        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && !field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else if (field) {
                field.classList.remove('is-invalid');
            }
        });

        // Check status selection
        if (!statusInput || !statusInput.value) {
            isValid = false;
            alert('{% trans "Please select an attendance status." %}');
        }

        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
