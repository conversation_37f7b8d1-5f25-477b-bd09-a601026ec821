from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum, Count
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    Account, AccountType, Payment, PaymentItem, StudentFee, FeeType, GradeFee,
    CostCenter, JournalEntry, FinancialYear, Bank, Invoice, InvoiceItem
)
from .forms import (
    AccountForm, JournalEntryForm, PaymentForm, InvoiceForm, CostCenterForm,
    BankForm, FeeTypeForm, GradeFeeForm, StudentFeeForm, FinancialYearForm, QuickPaymentForm
)
from students.models import Student

# Finance Dashboard
class FinanceDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current month data
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        # Calculate financial metrics
        context['total_revenue'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).aggregate(total=Sum('amount'))['total'] or 0

        context['total_expenses'] = JournalEntry.objects.filter(
            entry_date__gte=current_month,
            entry_date__lt=next_month,
            debit_amount__gt=0,
            account__account_type__type='expense'
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

        # Recent transactions for the dashboard
        context['recent_transactions'] = JournalEntry.objects.select_related(
            'account', 'account__account_type'
        ).order_by('-entry_date', '-created_at')[:5]

        # Outstanding payments
        context['outstanding_payments'] = StudentFee.objects.filter(
            is_paid=False
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Monthly statistics
        context['monthly_payments'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).count()

        # Net income
        context['net_income'] = context['total_revenue'] - context['total_expenses']

        # Pending and overdue payments
        pending_invoices = Invoice.objects.filter(status='sent')
        context['pending_payments'] = pending_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        context['pending_count'] = pending_invoices.count()

        overdue_invoices = Invoice.objects.filter(
            status='sent',
            due_date__lt=timezone.now().date()
        )
        context['overdue_payments'] = overdue_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        context['overdue_count'] = overdue_invoices.count()

        # Recent payments
        context['recent_payments'] = Payment.objects.select_related('student').order_by('-payment_date')[:10]

        # Outstanding fees
        outstanding_fees = StudentFee.objects.filter(is_paid=False)
        context['outstanding_fees_count'] = outstanding_fees.values('student').distinct().count()
        context['outstanding_fees_amount'] = outstanding_fees.aggregate(
            total=Sum('amount') - Sum('discount_amount')
        )['total'] or 0

        # Chart data (last 6 months)
        months = []
        revenue_data = []
        expense_data = []

        for i in range(6):
            month_start = (current_month - timedelta(days=i*30)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1)

            months.append(month_start.strftime('%b %Y'))

            month_revenue = Payment.objects.filter(
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('amount'))['total'] or 0

            month_expenses = JournalEntry.objects.filter(
                entry_date__gte=month_start,
                entry_date__lt=month_end,
                debit_amount__gt=0,
                account__account_type__type='expense'
            ).aggregate(total=Sum('debit_amount'))['total'] or 0

            revenue_data.append(float(month_revenue))
            expense_data.append(float(month_expenses))

        context['chart_labels'] = list(reversed(months))
        context['revenue_data'] = list(reversed(revenue_data))
        context['expense_data'] = list(reversed(expense_data))

        # Payment methods data
        payment_methods = Payment.objects.values('payment_method').annotate(
            count=Count('id'),
            total=Sum('amount')
        ).order_by('-total')

        context['payment_method_labels'] = [pm['payment_method'] for pm in payment_methods]
        context['payment_method_data'] = [float(pm['total']) for pm in payment_methods]

        return context

# Accounts Management Views
class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

class CostCenterView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center.html'

class CostCenterReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center_report.html'

class DailyEntriesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries.html'

class CreateDailyEntryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/create_daily_entry.html'

class DailyEntriesHistoryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries_history.html'

class FinancialYearsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/financial_years.html'

class BanksView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/banks.html'

class SafesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/safes.html'

class ReceiptVoucherView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/receipt_voucher.html'

class ExchangePermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/exchange_permission.html'

class TrialBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/trial_balance.html'

class AccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/account_statement.html'

class SearchReceiptsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/search_receipts.html'

class MonthlyAccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/monthly_statement.html'

class OpeningBalancesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balances.html'

class CashBankStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cash_bank_statement.html'

class BalanceSheetView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/balance_sheet.html'

class IncomeStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/income_statement.html'

# Student Accounts Views
class FeesItemsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_items.html'

class GroupedOptionalFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_fees.html'

class GradeFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grade_fees.html'

class FeesPaymentView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'finance/fees_payment.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related('student', 'received_by').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__first_name__icontains=search) |
                Q(student__last_name__icontains=search) |
                Q(receipt_number__icontains=search)
            )
        return queryset.order_by('-payment_date')


class PaymentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'finance/payment_form.html'
    permission_required = 'finance.add_payment'
    success_url = reverse_lazy('finance:payments')

    def form_valid(self, form):
        form.instance.received_by = self.request.user
        messages.success(self.request, _('Payment recorded successfully!'))
        return super().form_valid(form)


class PaymentHistoryView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'finance/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related('student', 'received_by').all()
        student_id = self.request.GET.get('student')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if date_from:
            queryset = queryset.filter(payment_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(payment_date__lte=date_to)

        return queryset.order_by('-payment_date')


class InvoiceListView(LoginRequiredMixin, ListView):
    model = Invoice
    template_name = 'finance/invoices.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = Invoice.objects.select_related('student', 'created_by').all()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-invoice_date')


class InvoiceCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'finance/invoice_form.html'
    permission_required = 'finance.add_invoice'
    success_url = reverse_lazy('finance:invoices')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        # Auto-generate invoice number
        last_invoice = Invoice.objects.order_by('-id').first()
        if last_invoice:
            last_number = int(last_invoice.invoice_number.split('-')[-1])
            form.instance.invoice_number = f"INV-{last_number + 1:06d}"
        else:
            form.instance.invoice_number = "INV-000001"

        messages.success(self.request, _('Invoice created successfully!'))
        return super().form_valid(form)

class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['account_types'] = AccountType.objects.prefetch_related('accounts').all()
        context['accounts'] = Account.objects.select_related('account_type', 'parent').all()
        return context


class AccountCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Account
    form_class = AccountForm
    template_name = 'finance/account_form.html'
    permission_required = 'finance.add_account'
    success_url = reverse_lazy('finance:accounts_tree')

    def form_valid(self, form):
        messages.success(self.request, _('Account created successfully!'))
        return super().form_valid(form)


class JournalEntriesView(LoginRequiredMixin, ListView):
    model = JournalEntry
    template_name = 'finance/journal_entries.html'
    context_object_name = 'entries'
    paginate_by = 20

    def get_queryset(self):
        queryset = JournalEntry.objects.select_related(
            'account', 'cost_center', 'created_by', 'posted_by'
        ).all()

        account_id = self.request.GET.get('account')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        is_posted = self.request.GET.get('is_posted')

        if account_id:
            queryset = queryset.filter(account_id=account_id)
        if date_from:
            queryset = queryset.filter(entry_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(entry_date__lte=date_to)
        if is_posted:
            queryset = queryset.filter(is_posted=is_posted == 'true')

        return queryset.order_by('-entry_date', '-created_at')


class JournalEntryCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = JournalEntry
    form_class = JournalEntryForm
    template_name = 'finance/journal_entry_form.html'
    permission_required = 'finance.add_journalentry'
    success_url = reverse_lazy('finance:journal_entries')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        # Auto-generate reference number
        last_entry = JournalEntry.objects.order_by('-id').first()
        if last_entry:
            last_number = int(last_entry.reference_number.split('-')[-1])
            form.instance.reference_number = f"JE-{last_number + 1:06d}"
        else:
            form.instance.reference_number = "JE-000001"

        messages.success(self.request, _('Journal entry created successfully!'))
        return super().form_valid(form)

class TaxCodeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_code.html'

class DailyPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_payments_report.html'

class StudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/student_debits.html'

class AccountsAggregateReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_aggregate_report.html'

class PaymentsByStagesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payments_by_stages.html'

class PaymentAccountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_account_report.html'

class ItemsSavesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/items_saves_report.html'

class TaxPaymentReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_payment_report.html'

# Payment Permissions Views
class PaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_permission.html'

class PayPaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/pay_permission.html'

class PermissionReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/permission_reports.html'

# Registration and Discounts Views
class RegistrationFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/registration_fees.html'

class DiscountSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_settings.html'

class DiscountSettingsDetailsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_details.html'

class AddDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/add_discount.html'

class RemoveStudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/remove_debits.html'

class DiscountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_report.html'

class BusPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/bus_payments_report.html'

class FeesRequestsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_requests.html'

class RecoveryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/recovery_report.html'

class PaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_statement.html'

class TotalPaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/total_payment_statement.html'

class UnpaidFeesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/unpaid_fees_report.html'

class AddStudentOpeningBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balance_student.html'

class PaymentRequestView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_request.html'

class GroupedDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_discount.html'

class GroupedPaymentTransactionsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_payments.html'

# Reports Views
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports.html'

class EInvoiceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/einvoice_report.html'

class ZakatIncomeReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/zakat_income_report.html'
