{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Schedule" %} - {{ block.super }}
    {% else %}
        {% trans "Add Schedule" %} - {{ block.super }}
    {% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .schedule-form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .schedule-form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .time-slot {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid #007bff;
    }
    .day-selector {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 15px 0;
    }
    .day-checkbox {
        flex: 1;
        min-width: 120px;
    }
    .day-checkbox input[type="checkbox"] {
        display: none;
    }
    .day-checkbox label {
        display: block;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    .day-checkbox input[type="checkbox"]:checked + label {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    .schedule-preview {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
    }
    .conflict-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
    }
    .form-floating > label {
        color: #6c757d;
    }
    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        color: #0d6efd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        {% if object %}
                            {% trans "Edit Schedule" %}
                        {% else %}
                            {% trans "Add Schedule" %}
                        {% endif %}
                    </h1>
                    <p class="text-muted">{% trans "Manage class schedules and timetables" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:schedules' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Schedules" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Schedule Form -->
        <div class="col-lg-8">
            <div class="card schedule-form-card">
                <div class="card-header schedule-form-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>{% trans "Schedule Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="scheduleForm">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    {{ form.class_subject }}
                                    <label for="{{ form.class_subject.id_for_label }}">{% trans "Class Subject" %} *</label>
                                </div>
                                {% if form.class_subject.errors %}
                                    <div class="text-danger small">{{ form.class_subject.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    {{ form.room_number }}
                                    <label for="{{ form.room_number.id_for_label }}">{% trans "Room Number" %}</label>
                                </div>
                                {% if form.room_number.errors %}
                                    <div class="text-danger small">{{ form.room_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Day Selection -->
                        <div class="mb-3">
                            <div class="form-floating">
                                {{ form.day_of_week }}
                                <label for="{{ form.day_of_week.id_for_label }}">{% trans "Day of Week" %} *</label>
                            </div>
                            {% if form.day_of_week.errors %}
                                <div class="text-danger small">{{ form.day_of_week.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Time Information -->
                        <div class="time-slot">
                            <h6 class="mb-3">
                                <i class="fas fa-clock me-2"></i>{% trans "Time Schedule" %}
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form.start_time }}
                                        <label for="{{ form.start_time.id_for_label }}">{% trans "Start Time" %} *</label>
                                    </div>
                                    {% if form.start_time.errors %}
                                        <div class="text-danger small">{{ form.start_time.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form.end_time }}
                                        <label for="{{ form.end_time.id_for_label }}">{% trans "End Time" %} *</label>
                                    </div>
                                    {% if form.end_time.errors %}
                                        <div class="text-danger small">{{ form.end_time.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="durationDisplay" readonly>
                                        <label for="durationDisplay">{% trans "Duration" %}</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form.period_number }}
                                        <label for="{{ form.period_number.id_for_label }}">{% trans "Period Number" %}</label>
                                    </div>
                                    {% if form.period_number.errors %}
                                        <div class="text-danger small">{{ form.period_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>



                        <!-- Conflict Warning -->
                        <div id="conflictWarning" class="conflict-warning" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                <div>
                                    <strong>{% trans "Schedule Conflict Detected!" %}</strong>
                                    <p class="mb-0" id="conflictMessage"></p>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'academics:schedules' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}
                                    {% trans "Update Schedule" %}
                                {% else %}
                                    {% trans "Save Schedule" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Schedule Preview -->
        <div class="col-lg-4">
            <div class="card schedule-form-card">
                <div class="card-body">
                    <div class="schedule-preview">
                        <h5 class="mb-3">{% trans "Schedule Preview" %}</h5>
                        <div id="schedulePreview">
                            <p class="mb-1" id="previewSubject">{% trans "Select class subject" %}</p>
                            <p class="mb-1" id="previewTime">{% trans "Set time" %}</p>
                            <p class="mb-1" id="previewDays">{% trans "Select days" %}</p>
                            <p class="mb-0" id="previewRoom">{% trans "Select room" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="card schedule-form-card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>{% trans "Quick Tips" %}
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Check for time conflicts before saving" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Ensure room availability" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Consider teacher availability" %}
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Set appropriate break times" %}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeField = document.getElementById('{{ form.start_time.id_for_label }}');
    const endTimeField = document.getElementById('{{ form.end_time.id_for_label }}');
    const durationDisplay = document.getElementById('durationDisplay');
    const classSubjectField = document.getElementById('{{ form.class_subject.id_for_label }}');
    const roomField = document.getElementById('{{ form.room_number.id_for_label }}');
    
    // Preview elements
    const previewSubject = document.getElementById('previewSubject');
    const previewTime = document.getElementById('previewTime');
    const previewDays = document.getElementById('previewDays');
    const previewRoom = document.getElementById('previewRoom');

    function calculateDuration() {
        if (startTimeField.value && endTimeField.value) {
            const start = new Date('2000-01-01 ' + startTimeField.value);
            const end = new Date('2000-01-01 ' + endTimeField.value);
            
            if (end > start) {
                const diff = end - start;
                const hours = Math.floor(diff / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                durationDisplay.value = `${hours}h ${minutes}m`;
            } else {
                durationDisplay.value = '';
            }
        }
    }

    function updatePreview() {
        // Update subject
        const selectedSubject = classSubjectField.options[classSubjectField.selectedIndex];
        previewSubject.textContent = selectedSubject.text || '{% trans "Select class subject" %}';

        // Update time
        if (startTimeField.value && endTimeField.value) {
            previewTime.textContent = `${startTimeField.value} - ${endTimeField.value}`;
        } else {
            previewTime.textContent = '{% trans "Set time" %}';
        }

        // Update day
        const dayField = document.getElementById('{{ form.day_of_week.id_for_label }}');
        const selectedDay = dayField.options[dayField.selectedIndex];
        previewDays.textContent = selectedDay.text || '{% trans "Select day" %}';

        // Update room
        const selectedRoom = roomField.options[roomField.selectedIndex];
        previewRoom.textContent = selectedRoom.text || '{% trans "Select room" %}';
    }

    function checkConflicts() {
        // This would typically make an AJAX call to check for conflicts
        // For now, we'll just show a placeholder
        const conflictWarning = document.getElementById('conflictWarning');
        const conflictMessage = document.getElementById('conflictMessage');
        
        if (startTimeField.value && endTimeField.value && classSubjectField.value) {
            // Simulate conflict check
            // In a real implementation, you'd make an AJAX call here
            conflictWarning.style.display = 'none';
        }
    }

    // Event listeners
    startTimeField.addEventListener('change', function() {
        calculateDuration();
        updatePreview();
        checkConflicts();
    });

    endTimeField.addEventListener('change', function() {
        calculateDuration();
        updatePreview();
        checkConflicts();
    });

    classSubjectField.addEventListener('change', function() {
        updatePreview();
        checkConflicts();
    });

    roomField.addEventListener('change', function() {
        updatePreview();
        checkConflicts();
    });

    // Day field
    const dayField = document.getElementById('{{ form.day_of_week.id_for_label }}');
    if (dayField) {
        dayField.addEventListener('change', function() {
            updatePreview();
            checkConflicts();
        });
    }

    // Form validation
    document.getElementById('scheduleForm').addEventListener('submit', function(e) {
        if (!dayField.value) {
            e.preventDefault();
            alert('{% trans "Please select a day of the week" %}');
            return;
        }

        if (startTimeField.value && endTimeField.value) {
            const start = new Date('2000-01-01 ' + startTimeField.value);
            const end = new Date('2000-01-01 ' + endTimeField.value);
            
            if (end <= start) {
                e.preventDefault();
                alert('{% trans "End time must be after start time" %}');
                endTimeField.focus();
                return;
            }
        }
    });

    // Initialize preview
    updatePreview();
    calculateDuration();
});
</script>
{% endblock %}
