{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Student ID Cards" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .id-card-container {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .id-card-container:hover {
        transform: translateY(-2px);
    }
    .id-card {
        width: 320px;
        height: 200px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        position: relative;
        overflow: hidden;
        margin: 1rem auto;
    }
    .id-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        transform: rotate(45deg);
    }
    .id-card-header {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        text-align: center;
        font-size: 0.9rem;
        font-weight: bold;
    }
    .id-card-body {
        padding: 1rem;
        display: flex;
        align-items: center;
        height: calc(100% - 40px);
    }
    .student-photo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 3px solid white;
        object-fit: cover;
        margin-right: 1rem;
    }
    .student-info h6 {
        margin: 0;
        font-size: 1rem;
        font-weight: bold;
    }
    .student-info p {
        margin: 0;
        font-size: 0.8rem;
        opacity: 0.9;
    }
    .id-number {
        position: absolute;
        bottom: 0.5rem;
        right: 1rem;
        font-size: 0.7rem;
        opacity: 0.8;
    }
    .card-template {
        border: 2px dashed #dee2e6;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        background: #f8f9fa;
        cursor: pointer;
        transition: all 0.3s;
    }
    .card-template:hover {
        border-color: #007bff;
        background: #e3f2fd;
    }
    .print-options {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
    }
    .batch-actions {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-id-card text-primary me-2"></i>{% trans "Student ID Cards" %}
                    </h2>
                    <p class="text-muted">{% trans "Generate and manage student identification cards" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#designModal">
                        <i class="fas fa-palette me-2"></i>{% trans "Design Template" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-print me-2"></i>{% trans "Print All" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="batch-actions">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-2">{% trans "Batch Operations" %}</h5>
                        <p class="mb-0">{% trans "Generate ID cards for multiple students at once" %}</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group">
                            <button class="btn btn-light">
                                <i class="fas fa-users me-2"></i>{% trans "Select Class" %}
                            </button>
                            <button class="btn btn-light">
                                <i class="fas fa-graduation-cap me-2"></i>{% trans "Select Grade" %}
                            </button>
                            <button class="btn btn-warning">
                                <i class="fas fa-magic me-2"></i>{% trans "Generate All" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card id-card-container">
                <div class="card-body">
                    <h5 class="card-title mb-3">{% trans "Filter Students" %}</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">{% trans "Search Student" %}</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ search_query }}" placeholder="{% trans 'Name or ID' %}">
                        </div>
                        <div class="col-md-3">
                            <label for="grade" class="form-label">{% trans "Grade" %}</label>
                            <select class="form-select" id="grade" name="grade">
                                <option value="">{% trans "All Grades" %}</option>
                                {% for grade in grades %}
                                    <option value="{{ grade.id }}" {% if selected_grade == grade.id|stringformat:"s" %}selected{% endif %}>
                                        {{ grade.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="class" class="form-label">{% trans "Class" %}</label>
                            <select class="form-select" id="class" name="class">
                                <option value="">{% trans "All Classes" %}</option>
                                {% for class in classes %}
                                    <option value="{{ class.id }}" {% if selected_class == class.id|stringformat:"s" %}selected{% endif %}>
                                        {{ class }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- ID Cards Grid -->
    <div class="row">
        {% for student in students %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card id-card-container">
                <div class="card-body text-center">
                    <div class="id-card">
                        <div class="id-card-header">
                            {{ school_name_ar|default:school_name }}
                        </div>
                        <div class="id-card-body">
                            {% if student.photo %}
                                <img src="{{ student.photo.url }}" alt="{{ student.full_name }}" class="student-photo">
                            {% else %}
                                <img src="{% static 'images/default-avatar.png' %}" alt="{{ student.full_name }}" class="student-photo">
                            {% endif %}
                            <div class="student-info">
                                <h6>{{ student.full_name }}</h6>
                                {% if student.current_class %}
                                    <p>{{ student.current_class }}</p>
                                {% endif %}
                                {% if current_academic_year %}
                                    <p>{{ current_academic_year.name }}</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="id-number">{{ student.student_id }}</div>
                    </div>
                    <div class="mt-3">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" title="{% trans 'Preview' %}"
                                    onclick="previewIDCard('{{ student.id }}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" title="{% trans 'Print' %}"
                                    onclick="printIDCard('{{ student.id }}')">
                                <i class="fas fa-print"></i>
                            </button>
                            <a href="{% url 'students:update' student.pk %}" class="btn btn-outline-info" title="{% trans 'Edit' %}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn btn-outline-warning" title="{% trans 'Download' %}"
                                    onclick="downloadIDCard('{{ student.id }}')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <small class="text-muted d-block mt-2">
                        <span class="badge bg-success">{% trans "Active" %}</span>
                    </small>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <h5>{% trans "No Students Found" %}</h5>
                <p>{% trans "No students match your search criteria. Try adjusting your filters." %}</p>
            </div>
        </div>
        {% endfor %}

    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="{% trans 'ID Cards pagination' %}">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_class %}class={{ selected_class }}&{% endif %}{% if selected_grade %}grade={{ selected_grade }}&{% endif %}page=1">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_class %}class={{ selected_class }}&{% endif %}{% if selected_grade %}grade={{ selected_grade }}&{% endif %}page={{ page_obj.previous_page_number }}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_class %}class={{ selected_class }}&{% endif %}{% if selected_grade %}grade={{ selected_grade }}&{% endif %}page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_class %}class={{ selected_class }}&{% endif %}{% if selected_grade %}grade={{ selected_grade }}&{% endif %}page={{ page_obj.next_page_number }}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_class %}class={{ selected_class }}&{% endif %}{% if selected_grade %}grade={{ selected_grade }}&{% endif %}page={{ page_obj.paginator.num_pages }}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>

            <div class="text-center text-muted">
                {% blocktrans with start=page_obj.start_index end=page_obj.end_index total=page_obj.paginator.count %}
                    Showing {{ start }} to {{ end }} of {{ total }} students
                {% endblocktrans %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Print Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card id-card-container">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-print me-2"></i>{% trans "Print Options" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="print-options">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">{% trans "Paper Size" %}</label>
                                <select class="form-select">
                                    <option>A4</option>
                                    <option>Letter</option>
                                    <option>Custom</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{% trans "Cards per Page" %}</label>
                                <select class="form-select">
                                    <option>4</option>
                                    <option>6</option>
                                    <option>8</option>
                                    <option>10</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">{% trans "Quality" %}</label>
                                <select class="form-select">
                                    <option>High (300 DPI)</option>
                                    <option>Medium (150 DPI)</option>
                                    <option>Low (72 DPI)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-success">
                                        <i class="fas fa-print me-2"></i>{% trans "Print Selected" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Card Modal -->
<div class="modal fade" id="newCardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Generate New ID Card" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="studentSelect" class="form-label">{% trans "Select Student" %}</label>
                        <select class="form-select" id="studentSelect">
                            <option value="">{% trans "Choose a student..." %}</option>
                            {% for student in all_students %}
                                <option value="{{ student.id }}">{{ student.user.first_name }} {{ student.user.last_name }} - {{ student.student_id }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="templateSelect" class="form-label">{% trans "Card Template" %}</label>
                        <select class="form-select" id="templateSelect">
                            <option value="default">{% trans "Default Template" %}</option>
                            <option value="modern">{% trans "Modern Template" %}</option>
                            <option value="classic">{% trans "Classic Template" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="academicYear" class="form-label">{% trans "Academic Year" %}</label>
                        <select class="form-select" id="academicYear">
                            {% for year in academic_years %}
                                <option value="{{ year.id }}" {% if year.is_current %}selected{% endif %}>{{ year.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Generate Card" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Design Template Modal -->
<div class="modal fade" id="designModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Design Card Template" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>{% trans "Template Options" %}</h6>
                        <form>
                            <div class="mb-3">
                                <label class="form-label">{% trans "School Name" %}</label>
                                <input type="text" class="form-control" value="مدرسة المستقبل الدولية">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{% trans "Background Color" %}</label>
                                <input type="color" class="form-control form-control-color" value="#667eea">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{% trans "Text Color" %}</label>
                                <input type="color" class="form-control form-control-color" value="#ffffff">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{% trans "Logo Upload" %}</label>
                                <input type="file" class="form-control" accept="image/*">
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <h6>{% trans "Preview" %}</h6>
                        <div class="id-card mx-auto">
                            <div class="id-card-header">
                                مدرسة المستقبل الدولية
                            </div>
                            <div class="id-card-body">
                                <img src="{% static 'images/default-avatar.png' %}" alt="Student" class="student-photo">
                                <div class="student-info">
                                    <h6>اسم الطالب</h6>
                                    <p>الصف</p>
                                    <p>السنة الدراسية</p>
                                </div>
                            </div>
                            <div class="id-number">ID</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Save Template" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Grade filter change handler
    document.getElementById('grade').addEventListener('change', function() {
        const gradeId = this.value;
        const classSelect = document.getElementById('class');

        // Clear class options except "All Classes"
        classSelect.innerHTML = '<option value="">{% trans "All Classes" %}</option>';

        if (gradeId) {
            // Filter classes by selected grade
            {% for class in classes %}
                if ('{{ class.grade.id }}' === gradeId) {
                    const option = document.createElement('option');
                    option.value = '{{ class.id }}';
                    option.textContent = '{{ class }}';
                    classSelect.appendChild(option);
                }
            {% endfor %}
        } else {
            // Show all classes
            {% for class in classes %}
                const option = document.createElement('option');
                option.value = '{{ class.id }}';
                option.textContent = '{{ class }}';
                classSelect.appendChild(option);
            {% endfor %}
        }
    });
});

// ID Card action functions
function previewIDCard(studentId) {
    // Open preview modal or new window
    window.open(`/students/id-card-preview/${studentId}/`, '_blank', 'width=800,height=600');
}

function printIDCard(studentId) {
    // Print specific ID card
    const printWindow = window.open(`/students/id-card-print/${studentId}/`, '_blank');
    printWindow.onload = function() {
        printWindow.print();
    };
}

function downloadIDCard(studentId) {
    // Download ID card as PDF
    window.location.href = `/students/id-card-download/${studentId}/`;
}

// Bulk actions
function printSelectedCards() {
    const selectedCards = document.querySelectorAll('input[name="selected_cards"]:checked');
    if (selectedCards.length === 0) {
        alert('{% trans "Please select at least one ID card to print." %}');
        return;
    }

    const studentIds = Array.from(selectedCards).map(cb => cb.value);
    const printUrl = `/students/id-cards-bulk-print/?ids=${studentIds.join(',')}`;
    const printWindow = window.open(printUrl, '_blank');
    printWindow.onload = function() {
        printWindow.print();
    };
}
</script>
{% endblock %}
