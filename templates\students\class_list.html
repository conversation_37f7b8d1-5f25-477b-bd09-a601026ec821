{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Classes" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .class-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .class-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .class-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .btn-primary {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 10px;
    }
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1.5rem;
    }
    .search-box:focus {
        border-color: #11998e;
        box-shadow: 0 0 0 0.2rem rgba(17, 153, 142, 0.25);
    }
    .grade-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">{% trans "Students" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Classes" %}</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users text-primary me-2"></i>{% trans "Class Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage student classes and grade levels" %}</p>
                </div>
                <div>
                    <a href="{% url 'students:class_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Class" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control search-box" placeholder="{% trans 'Search classes...' %}" id="searchInput">
                <button class="btn btn-outline-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="d-flex gap-2">
                <select class="form-select" id="gradeFilter">
                    <option value="">{% trans "All Grades" %}</option>
                    {% for grade in grades %}
                        <option value="{{ grade.id }}">{{ grade.name }}</option>
                    {% endfor %}
                </select>
                <select class="form-select" id="statusFilter">
                    <option value="">{% trans "All Status" %}</option>
                    <option value="active">{% trans "Active" %}</option>
                    <option value="inactive">{% trans "Inactive" %}</option>
                </select>
                <button class="btn btn-outline-secondary" id="resetFilters">
                    <i class="fas fa-undo me-2"></i>{% trans "Reset" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card class-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_classes }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Classes" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card class-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ active_classes }}</h4>
                    <p class="text-muted mb-0">{% trans "Active Classes" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card class-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ total_students }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card class-card">
                <div class="card-body text-center">
                    <i class="fas fa-layer-group fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ total_grades }}</h4>
                    <p class="text-muted mb-0">{% trans "Grade Levels" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Classes Grid -->
    <div class="row">
        {% for class in classes %}
        <div class="col-lg-4 col-md-6 mb-4" data-grade="{{ class.grade.id }}" data-status="{% if class.is_active %}active{% else %}inactive{% endif %}">
            <div class="card class-card">
                <div class="class-header">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h5 class="mb-1">{{ class.name }}</h5>
                                <p class="mb-0 opacity-75">{{ class.grade.name }}</p>
                            </div>
                            <span class="grade-badge bg-white text-primary">
                                {% trans "Grade" %} {{ class.grade.level }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="mb-1">{{ class.student_count }}</h6>
                                <small class="text-muted">{% trans "Students" %}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="mb-1">{{ class.capacity|default:"-" }}</h6>
                                <small class="text-muted">{% trans "Capacity" %}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-1">
                                {% if class.is_active %}
                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                {% else %}
                                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                {% endif %}
                            </h6>
                            <small class="text-muted">{% trans "Status" %}</small>
                        </div>
                    </div>
                    
                    {% if class.description %}
                    <p class="text-muted small mb-3">{{ class.description|truncatewords:15 }}</p>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" role="group">
                            <a href="{% url 'students:class_detail' class.pk %}" class="btn btn-sm btn-outline-info" title="{% trans 'View Details' %}">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'students:class_edit' class.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit' %}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete' %}" onclick="confirmDelete({{ class.pk }}, '{{ class.name }}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <small class="text-muted">
                            {% if class.room_number %}
                                <i class="fas fa-door-open me-1"></i>{{ class.room_number }}
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card class-card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "No classes found" %}</h5>
                    <p class="text-muted">{% trans "Start by creating your first class." %}</p>
                    <a href="{% url 'students:class_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add First Class" %}
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Confirm Delete" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "Are you sure you want to delete the class" %} "<span id="className"></span>"?</p>
                <p class="text-danger"><small>{% trans "This action cannot be undone and will affect all students in this class." %}</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <form method="post" id="deleteForm" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">{% trans "Delete" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const gradeFilter = document.getElementById('gradeFilter');
    const statusFilter = document.getElementById('statusFilter');
    const resetFilters = document.getElementById('resetFilters');
    const classCards = document.querySelectorAll('[data-grade]');

    // Search functionality
    searchInput.addEventListener('input', filterClasses);
    gradeFilter.addEventListener('change', filterClasses);
    statusFilter.addEventListener('change', filterClasses);

    // Reset filters
    resetFilters.addEventListener('click', function() {
        searchInput.value = '';
        gradeFilter.value = '';
        statusFilter.value = '';
        filterClasses();
    });

    function filterClasses() {
        const searchTerm = searchInput.value.toLowerCase();
        const gradeValue = gradeFilter.value;
        const statusValue = statusFilter.value;

        classCards.forEach(card => {
            const className = card.querySelector('h5').textContent.toLowerCase();
            const gradeName = card.querySelector('.opacity-75').textContent.toLowerCase();
            const gradeId = card.dataset.grade;
            const status = card.dataset.status;
            
            const matchesSearch = className.includes(searchTerm) || gradeName.includes(searchTerm);
            const matchesGrade = !gradeValue || gradeId === gradeValue;
            const matchesStatus = !statusValue || status === statusValue;
            
            card.style.display = matchesSearch && matchesGrade && matchesStatus ? '' : 'none';
        });
    }
});

function confirmDelete(classId, className) {
    document.getElementById('className').textContent = className;
    document.getElementById('deleteForm').action = `/students/classes/${classId}/delete/`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
