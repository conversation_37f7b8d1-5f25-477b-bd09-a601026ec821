{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "HR Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .hr-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .hr-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    .employee-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
    }
    .attendance-card {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
        border-radius: 15px;
    }
    .payroll-card {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        color: white;
        border-radius: 15px;
    }
    .quick-action-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        cursor: pointer;
        transition: transform 0.2s;
    }
    .quick-action-card:hover {
        transform: scale(1.05);
        color: white;
        text-decoration: none;
    }
    .employee-item {
        border-left: 3px solid #007bff;
        padding-left: 1rem;
        margin-bottom: 1rem;
    }
    .attendance-chart {
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users text-primary me-2"></i>{% trans "Human Resources Dashboard" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage employees, attendance, payroll, and HR operations" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Add Employee" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Report" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card employee-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_employees|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Employees" %}</p>
                    <small class="opacity-75">{% trans "Active staff members" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ attendance_rate|floatformat:1|default:0 }}%</h3>
                    <p class="mb-0">{% trans "Attendance Rate" %}</p>
                    <small class="opacity-75">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card payroll-card">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill fa-2x mb-2"></i>
                    <h3 class="mb-1">${{ monthly_payroll|floatformat:0|default:0 }}</h3>
                    <p class="mb-0">{% trans "Monthly Payroll" %}</p>
                    <small class="opacity-75">{% trans "Current month" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-clock fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ pending_leave_requests|default:0 }}</h3>
                    <p class="mb-0">{% trans "Pending Leaves" %}</p>
                    <small class="opacity-75">{% trans "Awaiting approval" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h5 class="mb-3">{% trans "Quick Actions" %}</h5>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'hr:employees' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Employees" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'hr:attendance' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Attendance" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'hr:payroll' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Payroll" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'hr:leaves' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-times fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Leave Requests" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'hr:performance' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Performance" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'hr:reports' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Reports" %}</h6>
                </div>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Recent Employees -->
        <div class="col-lg-6 mb-4">
            <div class="card hr-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>{% trans "Recent Employees" %}
                        </h5>
                        <a href="{% url 'hr:employees' %}" class="btn btn-sm btn-outline-primary">
                            {% trans "View All" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="employee-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                    <span class="text-white fw-bold">AM</span>
                                </div>
                                <div>
                                    <h6 class="mb-0">Ahmed Mohamed</h6>
                                    <small class="text-muted">{% trans "Math Teacher" %}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{% trans "Joined" %}: Jan 15</small>
                                <br>
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            </div>
                        </div>
                    </div>
                    <div class="employee-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                    <span class="text-white fw-bold">FH</span>
                                </div>
                                <div>
                                    <h6 class="mb-0">Fatima Hassan</h6>
                                    <small class="text-muted">{% trans "Science Teacher" %}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{% trans "Joined" %}: Jan 10</small>
                                <br>
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            </div>
                        </div>
                    </div>
                    <div class="employee-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                    <span class="text-white fw-bold">OK</span>
                                </div>
                                <div>
                                    <h6 class="mb-0">Omar Khalid</h6>
                                    <small class="text-muted">{% trans "IT Support" %}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{% trans "Joined" %}: Jan 8</small>
                                <br>
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Overview -->
        <div class="col-lg-6 mb-4">
            <div class="card hr-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>{% trans "Attendance Overview" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceChart" class="attendance-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Overview and Pending Tasks -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card hr-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>{% trans "Department Overview" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Department" %}</th>
                                    <th>{% trans "Head" %}</th>
                                    <th>{% trans "Employees" %}</th>
                                    <th>{% trans "Attendance" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-chalkboard-teacher text-primary me-2"></i>
                                            {% trans "Teaching" %}
                                        </div>
                                    </td>
                                    <td>Dr. Ahmed Ali</td>
                                    <td>45</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" style="width: 95%">95%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">{% trans "Excellent" %}</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-cogs text-info me-2"></i>
                                            {% trans "Administration" %}
                                        </div>
                                    </td>
                                    <td>Ms. Fatima Hassan</td>
                                    <td>15</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" style="width: 88%">88%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-primary">{% trans "Good" %}</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tools text-warning me-2"></i>
                                            {% trans "Support" %}
                                        </div>
                                    </td>
                                    <td>Mr. Omar Khalid</td>
                                    <td>12</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-warning" style="width: 82%">82%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-warning">{% trans "Average" %}</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-shield-alt text-secondary me-2"></i>
                                            {% trans "Security" %}
                                        </div>
                                    </td>
                                    <td>Mr. Khalid Salem</td>
                                    <td>8</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" style="width: 92%">92%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">{% trans "Good" %}</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Tasks -->
        <div class="col-lg-4 mb-4">
            <div class="card hr-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>{% trans "Pending Tasks" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Leave Approvals" %}
                            <span class="badge bg-warning rounded-pill">{{ pending_leaves|default:8 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Performance Reviews" %}
                            <span class="badge bg-info rounded-pill">{{ pending_reviews|default:12 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Payroll Processing" %}
                            <span class="badge bg-danger rounded-pill">{{ payroll_pending|default:1 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Document Verification" %}
                            <span class="badge bg-secondary rounded-pill">{{ docs_pending|default:5 }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Training Schedules" %}
                            <span class="badge bg-primary rounded-pill">{{ training_pending|default:3 }}</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">{% trans "Upcoming Events" %}</h6>
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Staff Meeting" %}</span>
                            <span class="text-primary">{% trans "Tomorrow" %}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Performance Reviews" %}</span>
                            <span class="text-warning">{% trans "Next Week" %}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Payroll Processing" %}</span>
                            <span class="text-success">{% trans "Month End" %}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Attendance Chart
        const ctx = document.getElementById('attendanceChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                datasets: [{
                    label: '{% trans "Present" %}',
                    data: [78, 82, 80, 85, 83, 45],
                    borderColor: '#11998e',
                    backgroundColor: 'rgba(17, 153, 142, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '{% trans "Absent" %}',
                    data: [7, 3, 5, 0, 2, 40],
                    borderColor: '#fc466b',
                    backgroundColor: 'rgba(252, 70, 107, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 85
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });
    });
</script>
{% endblock %}
