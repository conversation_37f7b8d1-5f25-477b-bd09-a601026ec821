{% load static %}
{% load i18n %}
{% load navigation_tags %}
<!DOCTYPE html>
<html lang="{% get_current_language as LANGUAGE_CODE %}{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "School ERP System" %}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    {% if LANGUAGE_CODE == 'ar' %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if user.is_authenticated %}
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="{% url 'accounts:dashboard' %}">
                    <i class="fas fa-school"></i> {% trans "School ERP" %}
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        {% block nav_items %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                                    <i class="fas fa-home"></i> {% trans "Home" %}
                                </a>
                            </li>

                            {% if user.user_type == 'admin' %}
                                <!-- Admin Area -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i> {% trans "Admin Area" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'core:admin_dashboard' %}">{% trans "Admin Dashboard" %}</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{% url 'accounts:permissions' %}">{% trans "User Permissions" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'core:sync' %}">{% trans "Sync" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'core:backup' %}">{% trans "Backup" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'core:settings' %}">{% trans "Settings" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'core:school_info' %}">{% trans "School Information" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:study_year' %}">{% trans "Study Year" %}</a></li>
                                    </ul>
                                </li>

                                <!-- Students Affairs -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-users"></i> {% trans "Students Affairs" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'students:add_parent' %}">{% trans "Add Parent" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'students:add_student' %}">{% trans "Add Student" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'students:list' %}">{% trans "Student Data" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'students:advanced_search' %}">{% trans "Advanced Search" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'students:distribute' %}">{% trans "Distribute Students" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'students:infractions' %}">{% trans "Students Infractions" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'students:vacation_requests' %}">{% trans "Vacation Requests" %}</a></li>
                                    </ul>
                                </li>

                                <!-- Accounts -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-chart-line"></i> {% trans "Accounts" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'finance:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:accounts_tree' %}">{% trans "Accounts Tree" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:cost_center' %}">{% trans "Cost Center" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:daily_entries' %}">{% trans "Daily Entries" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:trial_balance' %}">{% trans "Trial Balance" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:account_statement' %}">{% trans "Account Statement" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:balance_sheet' %}">{% trans "Balance Sheet" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:income_statement' %}">{% trans "Income Statement" %}</a></li>
                                    </ul>
                                </li>

                                <!-- Students Accounts -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-money-bill"></i> {% trans "Students Accounts" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'finance:fees_items' %}">{% trans "Fees Items" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:grade_fees' %}">{% trans "Grade Fees" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:fees_payment' %}">{% trans "Fees Payment" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:payment_reports' %}">{% trans "Payment Reports" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'finance:discount_settings' %}">{% trans "Discount Settings" %}</a></li>
                                    </ul>
                                </li>

                                <!-- Human Resources -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-user-tie"></i> {% trans "Human Resources" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'hr:employees' %}">{% trans "Employees" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'hr:attendance' %}">{% trans "Attendance" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'hr:payroll' %}">{% trans "Payroll" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'hr:permissions' %}">{% trans "Permissions" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'hr:holidays' %}">{% trans "Holidays" %}</a></li>
                                    </ul>
                                </li>
                            {% endif %}

                            {% if user.user_type in 'admin,teacher' %}
                                <!-- Academic Management -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-graduation-cap"></i> {% trans "Academic" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'academics:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:subjects' %}">{% trans "Subjects" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:teachers' %}">{% trans "Teachers" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:classes' %}">{% trans "Classes" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:schedules' %}">{% trans "Schedules" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:timetable' %}">{% trans "Timetable" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:grades' %}">{% trans "Grades" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:exams' %}">{% trans "Exams" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:attendance' %}">{% trans "Attendance" %}</a></li>
                                        <li><a class="dropdown-item" href="{% url 'academics:curriculum' %}">{% trans "Curriculum" %}</a></li>
                                    </ul>
                                </li>
                            {% endif %}

                            <!-- Reports -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-chart-bar"></i> {% trans "Reports" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'reports:dashboard' %}">{% trans "Reports Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:students' %}">{% trans "Student Reports" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:grades' %}">{% trans "Grades Report" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:class_performance' %}">{% trans "Class Performance" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:student_performance' %}">{% trans "Student Performance" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:student_attendance' %}">{% trans "Student Attendance" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:financial' %}">{% trans "Financial Reports" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:academic' %}">{% trans "Academic Reports" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:attendance' %}">{% trans "Attendance Reports" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:hr' %}">{% trans "HR Reports" %}</a></li>
                                </ul>
                            </li>
                        {% endblock %}
                    </ul>
                    
                    <ul class="navbar-nav">
                        <!-- Language Switcher -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-globe"></i>
                                {% if LANGUAGE_CODE == 'ar' %}العربية{% else %}English{% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <form method="post" action="{% url 'accounts:change_language' %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="en">
                                        <button type="submit" class="dropdown-item">English</button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="{% url 'accounts:change_language' %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="ar">
                                        <button type="submit" class="dropdown-item">العربية</button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                        
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user-edit"></i> {% trans "Profile" %}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt"></i> {% trans "Logout" %}
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    {% endif %}
    
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="skip-link">{% trans "Skip to main content" %}</a>

    <!-- Section Navigation -->
    {% if user.is_authenticated %}
        {% section_nav %}
    {% endif %}

    <!-- Main Content -->
    <main class="container-fluid" id="main-content">
        <!-- Breadcrumb Navigation -->
        {% if user.is_authenticated %}
            <div class="row">
                <div class="col-12">
                    {% breadcrumb_nav %}
                </div>
            </div>
        {% endif %}

        <!-- Messages -->
        {% if messages %}
            <div class="row">
                <div class="col-12">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>{{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-lg-start mt-5">
        <div class="text-center p-3">
            © 2025 {% trans "School ERP System" %} - {% trans "All rights reserved" %}
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
