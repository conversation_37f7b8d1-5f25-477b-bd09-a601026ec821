{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Electronic Registration" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .registration-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .registration-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .registration-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
    }
    .status-badge {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
    }
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1.5rem;
    }
    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">{% trans "Students" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Electronic Registration" %}</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-laptop text-primary me-2"></i>{% trans "Electronic Registration" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage online student registration applications" %}</p>
                </div>
                <div>
                    <a href="{% url 'students:add_electronic_registration' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "New Registration" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control search-box" placeholder="{% trans 'Search registrations...' %}" id="searchInput">
                <button class="btn btn-outline-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="d-flex gap-2">
                <select class="form-select" id="statusFilter">
                    <option value="">{% trans "All Status" %}</option>
                    <option value="submitted">{% trans "Submitted" %}</option>
                    <option value="under_review">{% trans "Under Review" %}</option>
                    <option value="approved">{% trans "Approved" %}</option>
                    <option value="rejected">{% trans "Rejected" %}</option>
                    <option value="completed">{% trans "Completed" %}</option>
                </select>
                <button class="btn btn-outline-secondary" id="resetFilters">
                    <i class="fas fa-undo me-2"></i>{% trans "Reset" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card registration-card">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_registrations }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Applications" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card registration-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ pending_registrations }}</h4>
                    <p class="text-muted mb-0">{% trans "Pending Review" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card registration-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ approved_registrations }}</h4>
                    <p class="text-muted mb-0">{% trans "Approved" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card registration-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ completed_registrations }}</h4>
                    <p class="text-muted mb-0">{% trans "Completed" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Registrations List -->
    <div class="card registration-card">
        <div class="registration-header">
            <div class="card-body">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>{% trans "Registration Applications" %}
                </h5>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="registrationsTable">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Application ID" %}</th>
                            <th>{% trans "Student Name" %}</th>
                            <th>{% trans "Desired Grade" %}</th>
                            <th>{% trans "Parent Contact" %}</th>
                            <th>{% trans "Submitted Date" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registration in registrations %}
                        <tr data-status="{{ registration.status }}">
                            <td>
                                <span class="badge bg-secondary">#{{ registration.id|stringformat:"05d" }}</span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ registration.full_name }}</strong>
                                    {% if registration.first_name_ar %}
                                        <br><small class="text-muted">{{ registration.first_name_ar }} {{ registration.last_name_ar }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ registration.desired_grade.name }}</span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ registration.father_name }}</strong>
                                    <br><small class="text-muted">
                                        <i class="fas fa-phone me-1"></i>{{ registration.father_phone }}
                                    </small>
                                    {% if registration.father_email %}
                                        <br><small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>{{ registration.father_email }}
                                        </small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <small>{{ registration.submitted_at|date:"M d, Y" }}</small>
                                <br><small class="text-muted">{{ registration.submitted_at|time:"H:i" }}</small>
                            </td>
                            <td>
                                {% if registration.status == 'submitted' %}
                                    <span class="status-badge bg-primary text-white">{% trans "Submitted" %}</span>
                                {% elif registration.status == 'under_review' %}
                                    <span class="status-badge bg-warning text-dark">{% trans "Under Review" %}</span>
                                {% elif registration.status == 'approved' %}
                                    <span class="status-badge bg-success text-white">{% trans "Approved" %}</span>
                                {% elif registration.status == 'rejected' %}
                                    <span class="status-badge bg-danger text-white">{% trans "Rejected" %}</span>
                                {% elif registration.status == 'completed' %}
                                    <span class="status-badge bg-info text-white">{% trans "Completed" %}</span>
                                {% else %}
                                    <span class="status-badge bg-secondary text-white">{{ registration.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-info" title="{% trans 'View Details' %}" onclick="viewRegistration({{ registration.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if registration.status == 'submitted' %}
                                        <button type="button" class="btn btn-sm btn-outline-success" title="{% trans 'Approve' %}" onclick="updateStatus({{ registration.id }}, 'approved')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="{% trans 'Reject' %}" onclick="updateStatus({{ registration.id }}, 'rejected')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    {% elif registration.status == 'approved' %}
                                        <button type="button" class="btn btn-sm btn-outline-primary" title="{% trans 'Complete Registration' %}" onclick="completeRegistration({{ registration.id }})">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-laptop fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">{% trans "No registration applications found" %}</h5>
                                <p class="text-muted">{% trans "New applications will appear here when submitted." %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="{% trans 'Pagination' %}" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">
                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                </span>
            </li>
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Registration Details Modal -->
<div class="modal fade" id="registrationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Registration Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="registrationDetails">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const resetFilters = document.getElementById('resetFilters');
    const table = document.getElementById('registrationsTable');
    const rows = table.querySelectorAll('tbody tr');

    // Search functionality
    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);

    // Reset filters
    resetFilters.addEventListener('click', function() {
        searchInput.value = '';
        statusFilter.value = '';
        filterTable();
    });

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;

        rows.forEach(row => {
            if (row.cells.length === 1) return; // Skip empty state row
            
            const studentName = row.cells[1].textContent.toLowerCase();
            const parentName = row.cells[3].textContent.toLowerCase();
            const status = row.dataset.status;
            
            const matchesSearch = studentName.includes(searchTerm) || parentName.includes(searchTerm);
            const matchesStatus = !statusValue || status === statusValue;
            
            row.style.display = matchesSearch && matchesStatus ? '' : 'none';
        });
    }
});

function viewRegistration(registrationId) {
    // Load registration details via AJAX
    fetch(`/students/electronic-registration/${registrationId}/`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('registrationDetails').innerHTML = html;
            new bootstrap.Modal(document.getElementById('registrationModal')).show();
        })
        .catch(error => {
            console.error('Error loading registration details:', error);
            alert('{% trans "Error loading registration details" %}');
        });
}

function updateStatus(registrationId, newStatus) {
    if (confirm('{% trans "Are you sure you want to update the status?" %}')) {
        // Update status via AJAX
        fetch(`/students/electronic-registration/${registrationId}/update-status/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({status: newStatus})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error updating status" %}');
            }
        })
        .catch(error => {
            console.error('Error updating status:', error);
            alert('{% trans "Error updating status" %}');
        });
    }
}

function completeRegistration(registrationId) {
    if (confirm('{% trans "This will create a new student record. Continue?" %}')) {
        // Complete registration via AJAX
        fetch(`/students/electronic-registration/${registrationId}/complete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{% trans "Student record created successfully!" %}');
                location.reload();
            } else {
                alert(data.message || '{% trans "Error completing registration" %}');
            }
        })
        .catch(error => {
            console.error('Error completing registration:', error);
            alert('{% trans "Error completing registration" %}');
        });
    }
}
</script>
{% endblock %}
