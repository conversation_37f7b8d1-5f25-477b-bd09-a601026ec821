{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Subject" %} - {{ object.name }}
    {% else %}
        {% trans "Add New Subject" %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
    }
    .btn-secondary {
        border-radius: 10px;
        padding: 0.75rem 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academics" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'academics:subjects' %}">{% trans "Subjects" %}</a></li>
            <li class="breadcrumb-item active">
                {% if object %}
                    {% trans "Edit Subject" %}
                {% else %}
                    {% trans "Add Subject" %}
                {% endif %}
            </li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h4 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        {% if object %}
                            {% trans "Edit Subject" %}: {{ object.name }}
                        {% else %}
                            {% trans "Add New Subject" %}
                        {% endif %}
                    </h4>
                    <p class="mb-0 opacity-75">
                        {% if object %}
                            {% trans "Update subject information and settings" %}
                        {% else %}
                            {% trans "Create a new subject for the academic curriculum" %}
                        {% endif %}
                    </p>
                </div>
                
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="fas fa-book text-primary me-2"></i>{% trans "Subject Name" %} *
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name_ar.id_for_label }}" class="form-label">
                                    <i class="fas fa-language text-primary me-2"></i>{% trans "Subject Name (Arabic)" %}
                                </label>
                                {{ form.name_ar }}
                                {% if form.name_ar.errors %}
                                    <div class="text-danger small mt-1">{{ form.name_ar.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">
                                    <i class="fas fa-hashtag text-primary me-2"></i>{% trans "Subject Code" %} *
                                </label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger small mt-1">{{ form.code.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.credit_hours.id_for_label }}" class="form-label">
                                    <i class="fas fa-clock text-primary me-2"></i>{% trans "Credit Hours" %}
                                </label>
                                {{ form.credit_hours }}
                                {% if form.credit_hours.errors %}
                                    <div class="text-danger small mt-1">{{ form.credit_hours.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left text-primary me-2"></i>{% trans "Description" %}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <i class="fas fa-toggle-on text-success me-2"></i>{% trans "Active Subject" %}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small mt-1">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:subjects' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}
                                    {% trans "Update Subject" %}
                                {% else %}
                                    {% trans "Create Subject" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(control) {
        if (!control.classList.contains('form-check-input')) {
            control.classList.add('form-control');
        }
    });
    
    // Auto-generate subject code from name
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    
    if (nameField && codeField && !codeField.value) {
        nameField.addEventListener('input', function() {
            const name = this.value.trim();
            if (name) {
                const code = name.substring(0, 3).toUpperCase() + Math.floor(Math.random() * 100);
                codeField.value = code;
            }
        });
    }
});
</script>
{% endblock %}
