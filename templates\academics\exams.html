{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Exam Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .exam-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .exam-card:hover {
        transform: translateY(-2px);
    }
    .exam-header {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .exam-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-upcoming {
        background-color: #fff3cd;
        color: #856404;
    }
    .status-ongoing {
        background-color: #d4edda;
        color: #155724;
    }
    .status-completed {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }
    .exam-type {
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    .type-quiz {
        background-color: #e6f3ff;
        color: #0c5460;
    }
    .type-midterm {
        background-color: #fff0e6;
        color: #856404;
    }
    .type-final {
        background-color: #ffe6e6;
        color: #721c24;
    }
    .type-assignment {
        background-color: #e6ffe6;
        color: #155724;
    }
    .calendar-widget {
        background: white;
        border-radius: 15px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .calendar-day {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1px;
        font-size: 0.875rem;
        cursor: pointer;
    }
    .calendar-day.exam-day {
        background-color: #fc466b;
        color: white;
    }
    .calendar-day.today {
        border: 2px solid #007bff;
        font-weight: bold;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .grade-progress {
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    .grade-bar {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-clipboard-list text-primary me-2"></i>{% trans "Exam Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage exams, assessments, and grading" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newExamModal">
                        <i class="fas fa-plus me-2"></i>{% trans "Schedule Exam" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export Schedule" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card exam-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="mb-1">{{ upcoming_exams|default:0 }}</h3>
                    <p class="mb-0">{% trans "Upcoming Exams" %}</p>
                    <small class="text-muted">{% trans "Next 7 days" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card exam-card">
                <div class="card-body text-center">
                    <i class="fas fa-play-circle fa-2x text-success mb-2"></i>
                    <h3 class="mb-1">{{ ongoing_exams|default:0 }}</h3>
                    <p class="mb-0">{% trans "Ongoing Exams" %}</p>
                    <small class="text-muted">{% trans "Currently active" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card exam-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                    <h3 class="mb-1">{{ completed_exams|default:15 }}</h3>
                    <p class="mb-0">{% trans "Completed Exams" %}</p>
                    <small class="text-muted">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card exam-card">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x text-primary mb-2"></i>
                    <h3 class="mb-1">{{ average_score|default:78 }}%</h3>
                    <p class="mb-0">{% trans "Average Score" %}</p>
                    <small class="text-muted">{% trans "All exams" %}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Exam Calendar -->
        <div class="col-lg-4 mb-4">
            <div class="calendar-widget">
                <h5 class="mb-3 text-center">
                    <i class="fas fa-calendar-alt me-2"></i>{% trans "Exam Calendar" %}
                </h5>
                <div class="text-center mb-3">
                    <h6>{{ current_month|default:"July 2024" }}</h6>
                </div>

                <!-- Calendar Grid -->
                <div class="d-flex justify-content-center">
                    <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 2px;">
                        <!-- Week Headers -->
                        <div class="text-center fw-bold">S</div>
                        <div class="text-center fw-bold">M</div>
                        <div class="text-center fw-bold">T</div>
                        <div class="text-center fw-bold">W</div>
                        <div class="text-center fw-bold">T</div>
                        <div class="text-center fw-bold">F</div>
                        <div class="text-center fw-bold">S</div>

                        <!-- Calendar Days -->
                        <div class="calendar-day">1</div>
                        <div class="calendar-day">2</div>
                        <div class="calendar-day">3</div>
                        <div class="calendar-day">4</div>
                        <div class="calendar-day">5</div>
                        <div class="calendar-day">6</div>
                        <div class="calendar-day">7</div>
                        <div class="calendar-day">8</div>
                        <div class="calendar-day">9</div>
                        <div class="calendar-day">10</div>
                        <div class="calendar-day">11</div>
                        <div class="calendar-day">12</div>
                        <div class="calendar-day">13</div>
                        <div class="calendar-day today">14</div>
                        <div class="calendar-day exam-day">15</div>
                        <div class="calendar-day">16</div>
                        <div class="calendar-day exam-day">17</div>
                        <div class="calendar-day">18</div>
                        <div class="calendar-day">19</div>
                        <div class="calendar-day exam-day">20</div>
                        <div class="calendar-day">21</div>
                        <div class="calendar-day">22</div>
                        <div class="calendar-day">23</div>
                        <div class="calendar-day">24</div>
                        <div class="calendar-day exam-day">25</div>
                        <div class="calendar-day">26</div>
                        <div class="calendar-day">27</div>
                        <div class="calendar-day">28</div>
                        <div class="calendar-day">29</div>
                        <div class="calendar-day">30</div>
                        <div class="calendar-day">31</div>
                    </div>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <span class="badge bg-danger me-2"></span>{% trans "Exam Days" %}
                    </small>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card exam-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkGradingModal">
                            <i class="fas fa-edit me-2"></i>{% trans "Bulk Grading" %}
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="fas fa-chart-bar me-2"></i>{% trans "Grade Report" %}
                        </button>
                        <button class="btn btn-outline-info">
                            <i class="fas fa-print me-2"></i>{% trans "Print Schedule" %}
                        </button>
                        <button class="btn btn-outline-warning">
                            <i class="fas fa-bell me-2"></i>{% trans "Send Reminders" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exam List -->
        <div class="col-lg-8">
            <!-- Filters -->
            <div class="filter-section">
                <h5 class="mb-3">{% trans "Filter Exams" %}</h5>
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <select class="form-select" name="status">
                            <option value="">{% trans "All Status" %}</option>
                            <option value="upcoming">{% trans "Upcoming" %}</option>
                            <option value="ongoing">{% trans "Ongoing" %}</option>
                            <option value="completed">{% trans "Completed" %}</option>
                            <option value="cancelled">{% trans "Cancelled" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="exam_type">
                            <option value="">{% trans "All Types" %}</option>
                            <option value="quiz">{% trans "Quiz" %}</option>
                            <option value="midterm">{% trans "Midterm" %}</option>
                            <option value="final">{% trans "Final" %}</option>
                            <option value="assignment">{% trans "Assignment" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="subject">
                            <option value="">{% trans "All Subjects" %}</option>
                            <option value="math">{% trans "Mathematics" %}</option>
                            <option value="arabic">{% trans "Arabic" %}</option>
                            <option value="science">{% trans "Science" %}</option>
                            <option value="english">{% trans "English" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>{% trans "Filter" %}
                        </button>
                    </div>
                </form>
            </div>

            <!-- Exam Cards -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card exam-card">
                        <div class="exam-header card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{% trans "Mathematics Quiz" %}</h6>
                                    <small>{% trans "Grade 5A - Mathematics" %}</small>
                                </div>
                                <span class="exam-type type-quiz">{% trans "Quiz" %}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Date" %}</small>
                                    <div class="fw-bold">Jul 15, 2024</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Time" %}</small>
                                    <div class="fw-bold">09:00 - 10:00</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Duration" %}</small>
                                    <div class="fw-bold">60 {% trans "minutes" %}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Total Marks" %}</small>
                                    <div class="fw-bold">50</div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <span class="exam-status status-upcoming">{% trans "Upcoming" %}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> {% trans "View" %}
                                </button>
                                <button class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-edit"></i> {% trans "Edit" %}
                                </button>
                                <button class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-times"></i> {% trans "Cancel" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card exam-card">
                        <div class="exam-header card-header" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{% trans "Arabic Midterm" %}</h6>
                                    <small>{% trans "Grade 4B - Arabic Language" %}</small>
                                </div>
                                <span class="exam-type type-midterm">{% trans "Midterm" %}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Date" %}</small>
                                    <div class="fw-bold">Jul 17, 2024</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Time" %}</small>
                                    <div class="fw-bold">10:00 - 12:00</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Duration" %}</small>
                                    <div class="fw-bold">120 {% trans "minutes" %}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">{% trans "Total Marks" %}</small>
                                    <div class="fw-bold">100</div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <span class="exam-status status-upcoming">{% trans "Upcoming" %}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> {% trans "View" %}
                                </button>
                                <button class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-edit"></i> {% trans "Edit" %}
                                </button>
                                <button class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-times"></i> {% trans "Cancel" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Exam Modal -->
<div class="modal fade" id="newExamModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Schedule New Exam" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="examName" class="form-label">{% trans "Exam Name" %}</label>
                            <input type="text" class="form-control" id="examName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="examNameAr" class="form-label">{% trans "Exam Name (Arabic)" %}</label>
                            <input type="text" class="form-control" id="examNameAr">
                        </div>
                        <div class="col-md-6">
                            <label for="examSubject" class="form-label">{% trans "Subject" %}</label>
                            <select class="form-select" id="examSubject" required>
                                <option value="">{% trans "Select Subject" %}</option>
                                <option value="math">{% trans "Mathematics" %}</option>
                                <option value="arabic">{% trans "Arabic Language" %}</option>
                                <option value="science">{% trans "Science" %}</option>
                                <option value="english">{% trans "English Language" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="examType" class="form-label">{% trans "Exam Type" %}</label>
                            <select class="form-select" id="examType" required>
                                <option value="">{% trans "Select Type" %}</option>
                                <option value="quiz">{% trans "Quiz" %}</option>
                                <option value="midterm">{% trans "Midterm" %}</option>
                                <option value="final">{% trans "Final" %}</option>
                                <option value="assignment">{% trans "Assignment" %}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="examDate" class="form-label">{% trans "Exam Date" %}</label>
                            <input type="date" class="form-control" id="examDate" required>
                        </div>
                        <div class="col-md-4">
                            <label for="startTime" class="form-label">{% trans "Start Time" %}</label>
                            <input type="time" class="form-control" id="startTime" required>
                        </div>
                        <div class="col-md-4">
                            <label for="endTime" class="form-label">{% trans "End Time" %}</label>
                            <input type="time" class="form-control" id="endTime" required>
                        </div>
                        <div class="col-md-6">
                            <label for="totalMarks" class="form-label">{% trans "Total Marks" %}</label>
                            <input type="number" class="form-control" id="totalMarks" min="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="passingMarks" class="form-label">{% trans "Passing Marks" %}</label>
                            <input type="number" class="form-control" id="passingMarks" min="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="roomNumber" class="form-label">{% trans "Room Number" %}</label>
                            <input type="text" class="form-control" id="roomNumber">
                        </div>
                        <div class="col-md-6">
                            <label for="duration" class="form-label">{% trans "Duration (Minutes)" %}</label>
                            <input type="number" class="form-control" id="duration" min="15" required>
                        </div>
                        <div class="col-12">
                            <label for="instructions" class="form-label">{% trans "Instructions" %}</label>
                            <textarea class="form-control" id="instructions" rows="3" placeholder="{% trans 'Exam instructions for students...' %}"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Schedule Exam" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Grading Modal -->
<div class="modal fade" id="bulkGradingModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Bulk Grading" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="gradingExam" class="form-label">{% trans "Select Exam" %}</label>
                    <select class="form-select" id="gradingExam">
                        <option value="">{% trans "Select Exam" %}</option>
                        <option value="1">{% trans "Mathematics Quiz - Grade 5A" %}</option>
                        <option value="2">{% trans "Arabic Midterm - Grade 4B" %}</option>
                        <option value="3">{% trans "Science Final - Grade 6A" %}</option>
                    </select>
                </div>

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "Student" %}</th>
                                <th>{% trans "Student ID" %}</th>
                                <th>{% trans "Marks Obtained" %}</th>
                                <th>{% trans "Grade" %}</th>
                                <th>{% trans "Remarks" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد علي</td>
                                <td>STU001</td>
                                <td><input type="number" class="form-control form-control-sm" min="0" max="50" placeholder="0"></td>
                                <td><span class="badge bg-secondary">-</span></td>
                                <td><input type="text" class="form-control form-control-sm" placeholder="{% trans 'Optional remarks' %}"></td>
                            </tr>
                            <tr>
                                <td>فاطمة أحمد حسن</td>
                                <td>STU002</td>
                                <td><input type="number" class="form-control form-control-sm" min="0" max="50" placeholder="0"></td>
                                <td><span class="badge bg-secondary">-</span></td>
                                <td><input type="text" class="form-control form-control-sm" placeholder="{% trans 'Optional remarks' %}"></td>
                            </tr>
                            <tr>
                                <td>محمد عبدالله سالم</td>
                                <td>STU003</td>
                                <td><input type="number" class="form-control form-control-sm" min="0" max="50" placeholder="0"></td>
                                <td><span class="badge bg-secondary">-</span></td>
                                <td><input type="text" class="form-control form-control-sm" placeholder="{% trans 'Optional remarks' %}"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Save Grades" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-calculate duration when times change
    const startTime = document.getElementById('startTime');
    const endTime = document.getElementById('endTime');
    const duration = document.getElementById('duration');

    function calculateDuration() {
        if (startTime.value && endTime.value) {
            const start = new Date('2000-01-01 ' + startTime.value);
            const end = new Date('2000-01-01 ' + endTime.value);
            const diffMs = end - start;
            const diffMins = Math.floor(diffMs / 60000);

            if (diffMins > 0) {
                duration.value = diffMins;
            }
        }
    }

    if (startTime && endTime && duration) {
        startTime.addEventListener('change', calculateDuration);
        endTime.addEventListener('change', calculateDuration);
    }

    // Auto-calculate passing marks (60% of total)
    const totalMarks = document.getElementById('totalMarks');
    const passingMarks = document.getElementById('passingMarks');

    if (totalMarks && passingMarks) {
        totalMarks.addEventListener('input', function() {
            const total = parseInt(this.value);
            if (total > 0) {
                passingMarks.value = Math.ceil(total * 0.6);
            }
        });
    }

    // Auto-submit filters
    const filterSelects = document.querySelectorAll('select[name="status"], select[name="exam_type"], select[name="subject"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Grade calculation in bulk grading
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', function() {
            const row = this.closest('tr');
            const gradeSpan = row.querySelector('.badge');
            const marks = parseInt(this.value) || 0;
            const maxMarks = parseInt(this.getAttribute('max')) || 100;
            const percentage = (marks / maxMarks) * 100;

            let grade = 'F';
            let gradeClass = 'bg-danger';

            if (percentage >= 90) {
                grade = 'A+';
                gradeClass = 'bg-success';
            } else if (percentage >= 80) {
                grade = 'A';
                gradeClass = 'bg-success';
            } else if (percentage >= 70) {
                grade = 'B';
                gradeClass = 'bg-primary';
            } else if (percentage >= 60) {
                grade = 'C';
                gradeClass = 'bg-warning';
            } else if (percentage >= 50) {
                grade = 'D';
                gradeClass = 'bg-secondary';
            }

            gradeSpan.className = `badge ${gradeClass}`;
            gradeSpan.textContent = grade;
        });
    });
});
</script>
{% endblock %}