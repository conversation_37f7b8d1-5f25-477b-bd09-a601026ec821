{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Vacation Requests" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .vacation-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .vacation-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .status-pending { border-left-color: #ffc107; }
    .status-approved { border-left-color: #28a745; }
    .status-rejected { border-left-color: #dc3545; }
    .status-cancelled { border-left-color: #6c757d; }
    .badge-pending { background-color: #ffc107; }
    .badge-approved { background-color: #28a745; }
    .badge-rejected { background-color: #dc3545; }
    .badge-cancelled { background-color: #6c757d; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-calendar-times me-2"></i>{% trans "Vacation Requests" %}</h2>
            <p class="text-muted">{% trans "Manage student vacation and leave requests" %}</p>
        </div>
        <div>
            <a href="{% url 'students:add_vacation_request' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{% trans "New Request" %}
            </a>
            <a href="{% url 'students:approve_vacation' %}" class="btn btn-success">
                <i class="fas fa-check me-2"></i>{% trans "Approve Requests" %}
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">{% trans "Search" %}</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="{% trans 'Search by student name...' %}"
                           value="{{ request.GET.search }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "Status" %}</label>
                    <select name="status" class="form-select">
                        <option value="">{% trans "All Statuses" %}</option>
                        <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                        <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                        <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>{% trans "Rejected" %}</option>
                        <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "Date Range" %}</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>{% trans "Filter" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">{{ pending_count|default:0 }}</h5>
                    <p class="card-text">{% trans "Pending" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">{{ approved_count|default:0 }}</h5>
                    <p class="card-text">{% trans "Approved" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-danger">{{ rejected_count|default:0 }}</h5>
                    <p class="card-text">{% trans "Rejected" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-secondary">{{ cancelled_count|default:0 }}</h5>
                    <p class="card-text">{% trans "Cancelled" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Vacation Requests List -->
    <div class="row">
        {% for request in vacation_requests %}
        <div class="col-lg-6 mb-4">
            <div class="card vacation-card status-{{ request.status }}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">{{ request.student.full_name }}</h6>
                        <small class="text-muted">{{ request.student.current_class }}</small>
                    </div>
                    <span class="badge badge-{{ request.status }}">
                        {{ request.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted d-block">{% trans "Start Date" %}</small>
                            <strong>{{ request.start_date }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">{% trans "End Date" %}</small>
                            <strong>{{ request.end_date }}</strong>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Duration" %}</small>
                        <strong>{{ request.duration_days }} {% trans "days" %}</strong>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Reason" %}</small>
                        <p class="mb-0">{{ request.reason|truncatewords:15 }}</p>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Requested By" %}</small>
                        <strong>{{ request.requested_by.get_full_name }}</strong>
                    </div>
                    {% if request.approved_by %}
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Approved By" %}</small>
                        <strong>{{ request.approved_by.get_full_name }}</strong>
                        <small class="text-muted">({{ request.approved_at }})</small>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" 
                                data-bs-target="#requestModal{{ request.id }}">
                            <i class="fas fa-eye me-1"></i>{% trans "View" %}
                        </button>
                        {% if request.status == 'pending' %}
                        <a href="#" class="btn btn-outline-success">
                            <i class="fas fa-check me-1"></i>{% trans "Approve" %}
                        </a>
                        <a href="#" class="btn btn-outline-danger">
                            <i class="fas fa-times me-1"></i>{% trans "Reject" %}
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Request Detail Modal -->
        <div class="modal fade" id="requestModal{{ request.id }}" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{% trans "Vacation Request Details" %}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{% trans "Student:" %}</strong> {{ request.student.full_name }}<br>
                                <strong>{% trans "Class:" %}</strong> {{ request.student.current_class }}<br>
                                <strong>{% trans "Start Date:" %}</strong> {{ request.start_date }}<br>
                                <strong>{% trans "End Date:" %}</strong> {{ request.end_date }}<br>
                                <strong>{% trans "Duration:" %}</strong> {{ request.duration_days }} {% trans "days" %}
                            </div>
                            <div class="col-md-6">
                                <strong>{% trans "Status:" %}</strong> 
                                <span class="badge badge-{{ request.status }}">{{ request.get_status_display }}</span><br>
                                <strong>{% trans "Requested By:" %}</strong> {{ request.requested_by.get_full_name }}<br>
                                <strong>{% trans "Request Date:" %}</strong> {{ request.created_at }}<br>
                                {% if request.approved_by %}
                                <strong>{% trans "Approved By:" %}</strong> {{ request.approved_by.get_full_name }}<br>
                                <strong>{% trans "Approval Date:" %}</strong> {{ request.approved_at }}
                                {% endif %}
                            </div>
                        </div>
                        <hr>
                        <div>
                            <strong>{% trans "Reason:" %}</strong>
                            <p>{{ request.reason }}</p>
                        </div>
                        {% if request.rejection_reason %}
                        <div class="alert alert-danger">
                            <strong>{% trans "Rejection Reason:" %}</strong>
                            <p class="mb-0">{{ request.rejection_reason }}</p>
                        </div>
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                        {% if request.status == 'pending' %}
                        <button type="button" class="btn btn-success">{% trans "Approve" %}</button>
                        <button type="button" class="btn btn-danger">{% trans "Reject" %}</button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">{% trans "No vacation requests found" %}</h4>
                <p class="text-muted">{% trans "No vacation requests match your current filters." %}</p>
                <a href="{% url 'students:add_vacation_request' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>{% trans "Create First Request" %}
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="{% trans 'Vacation requests pagination' %}">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "First" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "Previous" %}</a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">
                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                </span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "Next" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{% trans "Last" %}</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name="status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}
