from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from django.conf import settings
from core.models import BaseModel, AcademicYear


class Grade(BaseModel):
    """
    Grade/Level model (e.g., Grade 1, Grade 2, etc.)
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Grade Name')
    )

    name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Grade Name (Arabic)')
    )

    level = models.PositiveIntegerField(
        verbose_name=_('Grade Level')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    class Meta:
        verbose_name = _('Grade')
        verbose_name_plural = _('Grades')
        ordering = ['level']

    def __str__(self):
        return self.name


class Class(BaseModel):
    """
    Class/Section model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Class Name')
    )

    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='classes',
        verbose_name=_('Grade')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='classes',
        verbose_name=_('Academic Year')
    )

    class_teacher = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_classes',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('Class Teacher')
    )

    max_students = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Maximum Students')
    )

    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Room Number')
    )

    class Meta:
        verbose_name = _('Class')
        verbose_name_plural = _('Classes')
        unique_together = ['name', 'grade', 'academic_year']
        ordering = ['grade__level', 'name']

    def __str__(self):
        return f"{self.grade.name} - {self.name}"

    @property
    def current_students_count(self):
        return self.students.filter(is_active=True).count()


class Parent(BaseModel):
    """
    Parent model
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='parent_profile',
        limit_choices_to={'user_type': 'parent'},
        verbose_name=_('User Account')
    )

    father_name = models.CharField(
        max_length=100,
        verbose_name=_('Father Name')
    )

    mother_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Mother Name')
    )

    father_phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Father Phone')
    )

    mother_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Mother Phone')
    )

    father_occupation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Father Occupation')
    )

    mother_occupation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Mother Occupation')
    )

    father_workplace = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Father Workplace')
    )

    mother_workplace = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Mother Workplace')
    )

    home_address = models.TextField(
        verbose_name=_('Home Address')
    )

    emergency_contact = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Emergency Contact')
    )

    emergency_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Emergency Phone')
    )

    class Meta:
        verbose_name = _('Parent')
        verbose_name_plural = _('Parents')

    def __str__(self):
        return f"{self.father_name} - {self.user.username}"


class Student(BaseModel):
    """
    Student model
    """
    GENDER_CHOICES = (
        ('M', _('Male')),
        ('F', _('Female')),
    )

    BLOOD_TYPE_CHOICES = (
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='student_profile',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('User Account')
    )

    student_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Student ID')
    )

    admission_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Admission Number')
    )

    first_name = models.CharField(
        max_length=50,
        verbose_name=_('First Name')
    )

    last_name = models.CharField(
        max_length=50,
        verbose_name=_('Last Name')
    )

    first_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('First Name (Arabic)')
    )

    last_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Last Name (Arabic)')
    )

    date_of_birth = models.DateField(
        verbose_name=_('Date of Birth')
    )

    gender = models.CharField(
        max_length=1,
        choices=GENDER_CHOICES,
        verbose_name=_('Gender')
    )

    nationality = models.CharField(
        max_length=50,
        verbose_name=_('Nationality')
    )

    national_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('National ID')
    )

    passport_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Passport Number')
    )

    blood_type = models.CharField(
        max_length=3,
        choices=BLOOD_TYPE_CHOICES,
        blank=True,
        null=True,
        verbose_name=_('Blood Type')
    )

    parent = models.ForeignKey(
        Parent,
        on_delete=models.CASCADE,
        related_name='children',
        verbose_name=_('Parent')
    )

    current_class = models.ForeignKey(
        Class,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='students',
        verbose_name=_('Current Class')
    )

    admission_date = models.DateField(
        verbose_name=_('Admission Date')
    )

    previous_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Previous School')
    )

    medical_conditions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Medical Conditions')
    )

    allergies = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Allergies')
    )

    special_needs = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Special Needs')
    )

    photo = models.ImageField(
        upload_to='students/photos/',
        blank=True,
        null=True,
        verbose_name=_('Photo')
    )

    is_graduated = models.BooleanField(
        default=False,
        verbose_name=_('Is Graduated')
    )

    graduation_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Graduation Date')
    )

    class Meta:
        verbose_name = _('Student')
        verbose_name_plural = _('Students')
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.student_id})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name

    @property
    def age(self):
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )


class StudentDocument(BaseModel):
    """
    Student document model for managing student documents
    """
    DOCUMENT_TYPES = (
        ('birth_certificate', _('Birth Certificate')),
        ('passport', _('Passport')),
        ('medical_record', _('Medical Record')),
        ('previous_school_record', _('Previous School Record')),
        ('photo', _('Photo')),
        ('vaccination_record', _('Vaccination Record')),
        ('other', _('Other')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('Student')
    )

    document_type = models.CharField(
        max_length=30,
        choices=DOCUMENT_TYPES,
        verbose_name=_('Document Type')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Document Title')
    )

    file = models.FileField(
        upload_to='students/documents/',
        verbose_name=_('Document File')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_student_documents',
        verbose_name=_('Uploaded By')
    )

    is_verified = models.BooleanField(
        default=False,
        verbose_name=_('Is Verified')
    )

    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_student_documents',
        verbose_name=_('Verified By')
    )

    verified_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Verified At')
    )

    class Meta:
        verbose_name = _('Student Document')
        verbose_name_plural = _('Student Documents')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.title}"


class VacationRequest(BaseModel):
    """
    Student vacation request model
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='vacation_requests',
        verbose_name=_('Student')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    reason = models.TextField(
        verbose_name=_('Reason')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )

    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='requested_vacations',
        verbose_name=_('Requested By')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_vacations',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    rejection_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Rejection Reason')
    )

    class Meta:
        verbose_name = _('Vacation Request')
        verbose_name_plural = _('Vacation Requests')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.start_date} to {self.end_date}"

    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1


class StudentInfraction(BaseModel):
    """
    Student infraction/disciplinary action model
    """
    SEVERITY_CHOICES = (
        ('minor', _('Minor')),
        ('moderate', _('Moderate')),
        ('major', _('Major')),
        ('severe', _('Severe')),
    )

    ACTION_CHOICES = (
        ('warning', _('Warning')),
        ('detention', _('Detention')),
        ('suspension', _('Suspension')),
        ('expulsion', _('Expulsion')),
        ('community_service', _('Community Service')),
        ('parent_meeting', _('Parent Meeting')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='infractions',
        verbose_name=_('Student')
    )

    incident_date = models.DateField(
        verbose_name=_('Incident Date')
    )

    description = models.TextField(
        verbose_name=_('Description')
    )

    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_CHOICES,
        verbose_name=_('Severity')
    )

    action_taken = models.CharField(
        max_length=30,
        choices=ACTION_CHOICES,
        verbose_name=_('Action Taken')
    )

    reported_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reported_infractions',
        verbose_name=_('Reported By')
    )

    handled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='handled_infractions',
        verbose_name=_('Handled By')
    )

    parent_notified = models.BooleanField(
        default=False,
        verbose_name=_('Parent Notified')
    )

    parent_notified_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Parent Notified At')
    )

    follow_up_required = models.BooleanField(
        default=False,
        verbose_name=_('Follow-up Required')
    )

    follow_up_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Follow-up Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Additional Notes')
    )

    class Meta:
        verbose_name = _('Student Infraction')
        verbose_name_plural = _('Student Infractions')
        ordering = ['-incident_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.incident_date} ({self.severity})"


class StudentTransfer(BaseModel):
    """
    Student transfer model for tracking school transfers
    """
    TRANSFER_TYPES = (
        ('incoming', _('Incoming Transfer')),
        ('outgoing', _('Outgoing Transfer')),
        ('internal', _('Internal Transfer')),
    )

    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('completed', _('Completed')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='transfers',
        verbose_name=_('Student')
    )

    transfer_type = models.CharField(
        max_length=20,
        choices=TRANSFER_TYPES,
        verbose_name=_('Transfer Type')
    )

    from_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('From School')
    )

    to_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('To School')
    )

    from_class = models.ForeignKey(
        Class,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='outgoing_transfers',
        verbose_name=_('From Class')
    )

    to_class = models.ForeignKey(
        Class,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='incoming_transfers',
        verbose_name=_('To Class')
    )

    transfer_date = models.DateField(
        verbose_name=_('Transfer Date')
    )

    reason = models.TextField(
        verbose_name=_('Reason for Transfer')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )

    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='requested_transfers',
        verbose_name=_('Requested By')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transfers',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    documents_transferred = models.BooleanField(
        default=False,
        verbose_name=_('Documents Transferred')
    )

    fees_cleared = models.BooleanField(
        default=False,
        verbose_name=_('Fees Cleared')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Student Transfer')
        verbose_name_plural = _('Student Transfers')
        ordering = ['-transfer_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.transfer_type} ({self.transfer_date})"


class StudentAttachment(BaseModel):
    """
    Student attachment model for additional files and documents
    """
    ATTACHMENT_TYPES = (
        ('photo', _('Photo')),
        ('document', _('Document')),
        ('certificate', _('Certificate')),
        ('report', _('Report')),
        ('medical', _('Medical Document')),
        ('other', _('Other')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='attachments',
        verbose_name=_('Student')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Title')
    )

    attachment_type = models.CharField(
        max_length=20,
        choices=ATTACHMENT_TYPES,
        verbose_name=_('Attachment Type')
    )

    file = models.FileField(
        upload_to='students/attachments/',
        verbose_name=_('File')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_attachments',
        verbose_name=_('Uploaded By')
    )

    is_public = models.BooleanField(
        default=False,
        verbose_name=_('Is Public')
    )

    class Meta:
        verbose_name = _('Student Attachment')
        verbose_name_plural = _('Student Attachments')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.title}"


class ElectronicRegistration(BaseModel):
    """
    Electronic registration model for online student registration
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('submitted', _('Submitted')),
        ('under_review', _('Under Review')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('completed', _('Completed')),
    )

    # Basic Information
    first_name = models.CharField(
        max_length=50,
        verbose_name=_('First Name')
    )

    last_name = models.CharField(
        max_length=50,
        verbose_name=_('Last Name')
    )

    first_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('First Name (Arabic)')
    )

    last_name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Last Name (Arabic)')
    )

    date_of_birth = models.DateField(
        verbose_name=_('Date of Birth')
    )

    gender = models.CharField(
        max_length=1,
        choices=Student.GENDER_CHOICES,
        verbose_name=_('Gender')
    )

    nationality = models.CharField(
        max_length=50,
        verbose_name=_('Nationality')
    )

    # Parent Information
    father_name = models.CharField(
        max_length=100,
        verbose_name=_('Father Name')
    )

    father_phone = models.CharField(
        max_length=20,
        verbose_name=_('Father Phone')
    )

    father_email = models.EmailField(
        verbose_name=_('Father Email')
    )

    mother_name = models.CharField(
        max_length=100,
        verbose_name=_('Mother Name')
    )

    mother_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Mother Phone')
    )

    # Address Information
    home_address = models.TextField(
        verbose_name=_('Home Address')
    )

    # Academic Information
    desired_grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        verbose_name=_('Desired Grade')
    )

    previous_school = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Previous School')
    )

    # Registration Status
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    submitted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Submitted At')
    )

    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_registrations',
        verbose_name=_('Reviewed By')
    )

    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Reviewed At')
    )

    student = models.OneToOneField(
        Student,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='electronic_registration',
        verbose_name=_('Created Student')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Electronic Registration')
        verbose_name_plural = _('Electronic Registrations')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.desired_grade}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
