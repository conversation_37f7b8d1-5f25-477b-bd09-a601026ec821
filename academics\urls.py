from django.urls import path
from . import views

app_name = 'academics'

urlpatterns = [
    # Academic Dashboard
    path('', views.AcademicDashboardView.as_view(), name='dashboard'),

    # Study Year Management
    path('study-year/', views.StudyYearView.as_view(), name='study_year'),
    path('academic-years/', views.AcademicYearListView.as_view(), name='academic_years'),
    path('semesters/', views.SemesterListView.as_view(), name='semesters'),

    # Subject Management
    path('subjects/', views.SubjectListView.as_view(), name='subjects'),
    path('subjects/add/', views.SubjectCreateView.as_view(), name='subject_add'),
    path('subjects/<int:pk>/', views.SubjectDetailView.as_view(), name='subject_detail'),
    path('subjects/<int:pk>/edit/', views.SubjectUpdateView.as_view(), name='subject_edit'),
    path('subjects/<int:pk>/delete/', views.SubjectDeleteView.as_view(), name='subject_delete'),

    # Teacher Management
    path('teachers/', views.TeacherListView.as_view(), name='teachers'),
    path('teachers/add/', views.TeacherCreateView.as_view(), name='teacher_add'),
    path('teachers/<int:pk>/', views.TeacherDetailView.as_view(), name='teacher_detail'),
    path('teachers/<int:pk>/edit/', views.TeacherUpdateView.as_view(), name='teacher_edit'),

    # Class Management
    path('classes/', views.ClassListView.as_view(), name='classes'),
    path('classes/add/', views.ClassCreateView.as_view(), name='class_add'),
    path('classes/<int:pk>/', views.ClassDetailView.as_view(), name='class_detail'),
    path('classes/<int:pk>/edit/', views.ClassUpdateView.as_view(), name='class_edit'),

    # Schedule Management
    path('schedules/', views.ScheduleListView.as_view(), name='schedules'),
    path('schedules/add/', views.ScheduleCreateView.as_view(), name='schedule_add'),
    path('schedules/create/', views.ScheduleCreateView.as_view(), name='schedule_create'),  # Alternative name
    path('schedules/<int:pk>/', views.ScheduleDetailView.as_view(), name='schedule_detail'),
    path('schedules/<int:pk>/edit/', views.ScheduleUpdateView.as_view(), name='schedule_edit'),
    path('schedules/<int:pk>/update/', views.ScheduleUpdateView.as_view(), name='schedule_update'),  # Alternative name
    path('schedules/timetable/', views.TimetableView.as_view(), name='timetable'),

    # Grades and Assessment
    path('grades/', views.GradeManagementView.as_view(), name='grades'),
    path('grades/create/', views.GradeCreateView.as_view(), name='grade_create'),
    path('grades/<int:pk>/', views.GradeDetailView.as_view(), name='grade_detail'),
    path('grades/<int:pk>/update/', views.GradeUpdateView.as_view(), name='grade_update'),
    path('grades/entry/', views.GradeEntryView.as_view(), name='grade_entry'),
    path('grades/reports/', views.GradeReportsView.as_view(), name='grade_reports'),
    path('grades/report/', views.GradeReportView.as_view(), name='grade_report'),
    path('grades/transcripts/', views.TranscriptsView.as_view(), name='transcripts'),

    # Exams
    path('exams/', views.ExamListView.as_view(), name='exams'),
    path('exams/add/', views.ExamCreateView.as_view(), name='exam_add'),
    path('exams/<int:pk>/', views.ExamDetailView.as_view(), name='exam_detail'),
    path('exams/<int:pk>/edit/', views.ExamUpdateView.as_view(), name='exam_edit'),
    path('exams/schedule/', views.ExamScheduleView.as_view(), name='exam_schedule'),
    path('exams/results/', views.ExamResultsView.as_view(), name='exam_results'),

    # Attendance
    path('attendance/', views.AttendanceView.as_view(), name='attendance'),
    path('attendance/create/', views.AttendanceCreateView.as_view(), name='attendance_create'),
    path('attendance/<int:pk>/', views.AttendanceDetailView.as_view(), name='attendance_detail'),
    path('attendance/<int:pk>/update/', views.AttendanceUpdateView.as_view(), name='attendance_update'),
    path('attendance/take/', views.TakeAttendanceView.as_view(), name='take_attendance'),
    path('attendance/report/', views.AttendanceReportView.as_view(), name='attendance_report'),
    path('attendance/reports/', views.AttendanceReportsView.as_view(), name='attendance_reports'),
    path('attendance/summary/', views.AttendanceSummaryView.as_view(), name='attendance_summary'),

    # Curriculum and Syllabus
    path('curriculum/', views.CurriculumView.as_view(), name='curriculum'),
    path('syllabus/', views.SyllabusView.as_view(), name='syllabus'),
    path('lesson-plans/', views.LessonPlansView.as_view(), name='lesson_plans'),

    # Academic Reports
    path('reports/', views.AcademicReportsView.as_view(), name='reports'),
    path('reports/progress/', views.ProgressReportsView.as_view(), name='progress_reports'),
    path('reports/performance/', views.PerformanceReportsView.as_view(), name='performance_reports'),
    path('reports/class-summary/', views.ClassSummaryReportsView.as_view(), name='class_summary_reports'),
]
