{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Student Infractions" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .infraction-card {
        border-left: 4px solid #dc3545;
        transition: all 0.3s ease;
    }
    .infraction-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .severity-minor { border-left-color: #ffc107; }
    .severity-moderate { border-left-color: #fd7e14; }
    .severity-major { border-left-color: #dc3545; }
    .severity-severe { border-left-color: #6f42c1; }
    .badge-minor { background-color: #ffc107; }
    .badge-moderate { background-color: #fd7e14; }
    .badge-major { background-color: #dc3545; }
    .badge-severe { background-color: #6f42c1; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-exclamation-triangle me-2"></i>{% trans "Student Infractions" %}</h2>
            <p class="text-muted">{% trans "Manage student disciplinary records and infractions" %}</p>
        </div>
        <div>
            <a href="{% url 'students:add_infraction' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{% trans "Add Infraction" %}
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">{% trans "Search" %}</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="{% trans 'Search by student name or description...' %}"
                           value="{{ request.GET.search }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "Severity" %}</label>
                    <select name="severity" class="form-select">
                        <option value="">{% trans "All Severities" %}</option>
                        <option value="minor" {% if request.GET.severity == 'minor' %}selected{% endif %}>{% trans "Minor" %}</option>
                        <option value="moderate" {% if request.GET.severity == 'moderate' %}selected{% endif %}>{% trans "Moderate" %}</option>
                        <option value="major" {% if request.GET.severity == 'major' %}selected{% endif %}>{% trans "Major" %}</option>
                        <option value="severe" {% if request.GET.severity == 'severe' %}selected{% endif %}>{% trans "Severe" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "Action Taken" %}</label>
                    <select name="action" class="form-select">
                        <option value="">{% trans "All Actions" %}</option>
                        <option value="warning" {% if request.GET.action == 'warning' %}selected{% endif %}>{% trans "Warning" %}</option>
                        <option value="detention" {% if request.GET.action == 'detention' %}selected{% endif %}>{% trans "Detention" %}</option>
                        <option value="suspension" {% if request.GET.action == 'suspension' %}selected{% endif %}>{% trans "Suspension" %}</option>
                        <option value="expulsion" {% if request.GET.action == 'expulsion' %}selected{% endif %}>{% trans "Expulsion" %}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>{% trans "Filter" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Infractions List -->
    <div class="row">
        {% for infraction in infractions %}
        <div class="col-lg-6 mb-4">
            <div class="card infraction-card severity-{{ infraction.severity }}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">{{ infraction.student.full_name }}</h6>
                        <small class="text-muted">{{ infraction.incident_date }}</small>
                    </div>
                    <span class="badge badge-{{ infraction.severity }}">
                        {{ infraction.get_severity_display }}
                    </span>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ infraction.description|truncatewords:20 }}</p>
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted d-block">{% trans "Action Taken" %}</small>
                            <strong>{{ infraction.get_action_taken_display }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">{% trans "Reported By" %}</small>
                            <strong>{{ infraction.reported_by.get_full_name }}</strong>
                        </div>
                    </div>
                    {% if infraction.parent_notified %}
                    <div class="mt-2">
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>{% trans "Parent Notified" %}
                        </span>
                    </div>
                    {% endif %}
                    {% if infraction.follow_up_required %}
                    <div class="mt-2">
                        <span class="badge bg-warning">
                            <i class="fas fa-clock me-1"></i>{% trans "Follow-up Required" %}
                        </span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" 
                                data-bs-target="#infractionModal{{ infraction.id }}">
                            <i class="fas fa-eye me-1"></i>{% trans "View" %}
                        </button>
                        <a href="#" class="btn btn-outline-secondary">
                            <i class="fas fa-edit me-1"></i>{% trans "Edit" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Infraction Detail Modal -->
        <div class="modal fade" id="infractionModal{{ infraction.id }}" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{% trans "Infraction Details" %}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{% trans "Student:" %}</strong> {{ infraction.student.full_name }}<br>
                                <strong>{% trans "Date:" %}</strong> {{ infraction.incident_date }}<br>
                                <strong>{% trans "Severity:" %}</strong> 
                                <span class="badge badge-{{ infraction.severity }}">{{ infraction.get_severity_display }}</span><br>
                                <strong>{% trans "Action Taken:" %}</strong> {{ infraction.get_action_taken_display }}
                            </div>
                            <div class="col-md-6">
                                <strong>{% trans "Reported By:" %}</strong> {{ infraction.reported_by.get_full_name }}<br>
                                {% if infraction.handled_by %}
                                <strong>{% trans "Handled By:" %}</strong> {{ infraction.handled_by.get_full_name }}<br>
                                {% endif %}
                                <strong>{% trans "Parent Notified:" %}</strong> 
                                {% if infraction.parent_notified %}
                                    <span class="text-success">{% trans "Yes" %}</span>
                                    {% if infraction.parent_notified_at %}
                                        ({{ infraction.parent_notified_at }})
                                    {% endif %}
                                {% else %}
                                    <span class="text-danger">{% trans "No" %}</span>
                                {% endif %}
                            </div>
                        </div>
                        <hr>
                        <div>
                            <strong>{% trans "Description:" %}</strong>
                            <p>{{ infraction.description }}</p>
                        </div>
                        {% if infraction.notes %}
                        <div>
                            <strong>{% trans "Additional Notes:" %}</strong>
                            <p>{{ infraction.notes }}</p>
                        </div>
                        {% endif %}
                        {% if infraction.follow_up_required and infraction.follow_up_date %}
                        <div class="alert alert-warning">
                            <i class="fas fa-clock me-2"></i>
                            <strong>{% trans "Follow-up Required:" %}</strong> {{ infraction.follow_up_date }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                        <a href="#" class="btn btn-primary">{% trans "Edit Infraction" %}</a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">{% trans "No infractions found" %}</h4>
                <p class="text-muted">{% trans "No student infractions match your current filters." %}</p>
                <a href="{% url 'students:add_infraction' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>{% trans "Add First Infraction" %}
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="{% trans 'Infractions pagination' %}">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}">{% trans "First" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}">{% trans "Previous" %}</a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">
                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                </span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}">{% trans "Next" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}">{% trans "Last" %}</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name="severity"], select[name="action"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}
