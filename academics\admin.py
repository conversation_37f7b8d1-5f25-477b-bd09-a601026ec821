from django.contrib import admin
from .models import (
    Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, Curriculum, CurriculumSubject
)

# Note: AcademicYear is registered in core/admin.py since it's defined in core/models.py


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    """
    Subject admin
    """
    list_display = ('name', 'code', 'credit_hours', 'is_active')
    list_filter = ('credit_hours', 'is_active')
    search_fields = ('name', 'name_ar', 'code')
    ordering = ('name',)


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    """
    Teacher admin
    """
    list_display = ('user', 'employee_id', 'department', 'hire_date', 'experience_years', 'is_active')
    list_filter = ('department', 'hire_date', 'is_active')
    search_fields = ('user__first_name', 'user__last_name', 'employee_id', 'qualification')
    raw_id_fields = ('user',)
    filter_horizontal = ('subjects',)
    date_hierarchy = 'hire_date'


@admin.register(ClassSubject)
class ClassSubjectAdmin(admin.ModelAdmin):
    """
    Class Subject admin
    """
    list_display = ('class_obj', 'subject', 'teacher', 'academic_year', 'weekly_hours', 'is_active')
    list_filter = ('academic_year', 'subject', 'is_active')
    search_fields = ('class_obj__name', 'subject__name', 'teacher__user__first_name', 'teacher__user__last_name')
    raw_id_fields = ('class_obj', 'subject', 'teacher', 'academic_year')


@admin.register(Schedule)
class ScheduleAdmin(admin.ModelAdmin):
    """
    Schedule admin
    """
    list_display = ('class_subject', 'day_of_week', 'start_time', 'end_time', 'room_number', 'is_active')
    list_filter = ('day_of_week', 'class_subject__academic_year', 'is_active')
    search_fields = ('class_subject__class_name__name', 'class_subject__subject__name', 'room_number')
    raw_id_fields = ('class_subject',)


@admin.register(Exam)
class ExamAdmin(admin.ModelAdmin):
    """
    Exam admin
    """
    list_display = ('name', 'class_subject', 'exam_type', 'exam_date', 'total_marks', 'is_published', 'is_completed')
    list_filter = ('exam_type', 'exam_date', 'is_published', 'class_subject__academic_year')
    search_fields = ('name', 'name_ar', 'class_subject__class_name__name', 'class_subject__subject__name')
    raw_id_fields = ('class_subject', 'created_by')
    date_hierarchy = 'exam_date'
    readonly_fields = ('is_completed',)

    def is_completed(self, obj):
        return obj.is_completed
    is_completed.boolean = True
    is_completed.short_description = 'Is Completed'


@admin.register(StudentGrade)
class StudentGradeAdmin(admin.ModelAdmin):
    """
    Student Grade admin
    """
    list_display = ('student', 'exam', 'marks_obtained', 'total_marks', 'percentage', 'grade_letter', 'graded_by')
    list_filter = ('grade_letter', 'exam__exam_type', 'exam__exam_date', 'graded_at')
    search_fields = ('student__first_name', 'student__last_name', 'exam__name')
    raw_id_fields = ('student', 'exam', 'graded_by')
    readonly_fields = ('percentage', 'grade_letter', 'graded_at')

    def total_marks(self, obj):
        return obj.exam.total_marks
    total_marks.short_description = 'Total Marks'


@admin.register(StudentAttendance)
class StudentAttendanceAdmin(admin.ModelAdmin):
    """
    Student Attendance admin
    """
    list_display = ('student', 'class_subject', 'date', 'status', 'marked_by')
    list_filter = ('status', 'date', 'class_subject__subject', 'class_subject__academic_year')
    search_fields = ('student__first_name', 'student__last_name', 'class_subject__subject__name')
    raw_id_fields = ('student', 'class_subject', 'marked_by')
    date_hierarchy = 'date'


@admin.register(Curriculum)
class CurriculumAdmin(admin.ModelAdmin):
    """
    Curriculum admin
    """
    list_display = ('name', 'grade', 'academic_year', 'is_active', 'created_by')
    list_filter = ('grade', 'academic_year', 'is_active')
    search_fields = ('name', 'name_ar', 'grade__name')
    raw_id_fields = ('grade', 'academic_year', 'created_by')


@admin.register(CurriculumSubject)
class CurriculumSubjectAdmin(admin.ModelAdmin):
    """
    Curriculum Subject admin
    """
    list_display = ('curriculum', 'subject', 'weekly_hours', 'semester', 'is_mandatory')
    list_filter = ('semester', 'is_mandatory', 'curriculum__grade')
    search_fields = ('curriculum__name', 'subject__name')
    raw_id_fields = ('curriculum', 'subject')
