from django.contrib import admin
from .models import School, AcademicYear, Semester


@admin.register(School)
class SchoolAdmin(admin.ModelAdmin):
    """
    School admin
    """
    list_display = ('name', 'code', 'phone', 'email', 'principal_name', 'is_active')
    list_filter = ('is_active', 'established_date')
    search_fields = ('name', 'name_ar', 'code', 'email', 'principal_name')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(AcademicYear)
class AcademicYearAdmin(admin.ModelAdmin):
    """
    Academic Year admin
    """
    list_display = ('name', 'start_date', 'end_date', 'is_current', 'is_active')
    list_filter = ('is_current', 'is_active', 'start_date')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Semester)
class SemesterAdmin(admin.ModelAdmin):
    """
    Semester admin
    """
    list_display = ('name', 'academic_year', 'start_date', 'end_date', 'is_current', 'is_active')
    list_filter = ('is_current', 'is_active', 'academic_year', 'start_date')
    search_fields = ('name', 'academic_year__name')
    readonly_fields = ('created_at', 'updated_at')
