{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Grade Levels" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .grade-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .grade-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .grade-header {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .btn-primary {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        border: none;
        border-radius: 10px;
    }
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1.5rem;
    }
    .search-box:focus {
        border-color: #fc466b;
        box-shadow: 0 0 0 0.2rem rgba(252, 70, 107, 0.25);
    }
    .level-badge {
        font-size: 1.2rem;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">{% trans "Students" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Grade Levels" %}</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-layer-group text-primary me-2"></i>{% trans "Grade Level Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage academic grade levels and progression" %}</p>
                </div>
                <div>
                    <a href="{% url 'students:grade_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Grade Level" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control search-box" placeholder="{% trans 'Search grade levels...' %}" id="searchInput">
                <button class="btn btn-outline-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="d-flex gap-2">
                <select class="form-select" id="statusFilter">
                    <option value="">{% trans "All Status" %}</option>
                    <option value="active">{% trans "Active" %}</option>
                    <option value="inactive">{% trans "Inactive" %}</option>
                </select>
                <button class="btn btn-outline-secondary" id="resetFilters">
                    <i class="fas fa-undo me-2"></i>{% trans "Reset" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-layer-group fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_grades }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Grades" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ active_grades }}</h4>
                    <p class="text-muted mb-0">{% trans "Active Grades" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ total_classes }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Classes" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card grade-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ total_students }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Students" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Grades Grid -->
    <div class="row">
        {% for grade in grades %}
        <div class="col-lg-4 col-md-6 mb-4" data-status="{% if grade.is_active %}active{% else %}inactive{% endif %}">
            <div class="card grade-card">
                <div class="grade-header">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="level-badge bg-white text-primary me-3">
                                    {{ grade.level }}
                                </div>
                                <div>
                                    <h5 class="mb-1">{{ grade.name }}</h5>
                                    <p class="mb-0 opacity-75">{% trans "Grade Level" %} {{ grade.level }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="mb-1">{{ grade.class_count }}</h6>
                                <small class="text-muted">{% trans "Classes" %}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-1">{{ grade.student_count }}</h6>
                            <small class="text-muted">{% trans "Students" %}</small>
                        </div>
                    </div>
                    
                    {% if grade.description %}
                    <p class="text-muted small mb-3">{{ grade.description|truncatewords:15 }}</p>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" role="group">
                            <a href="{% url 'students:grade_detail' grade.pk %}" class="btn btn-sm btn-outline-info" title="{% trans 'View Details' %}">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'students:grade_edit' grade.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit' %}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete' %}" onclick="confirmDelete({{ grade.pk }}, '{{ grade.name }}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div>
                            {% if grade.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-danger">{% trans "Inactive" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card grade-card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "No grade levels found" %}</h5>
                    <p class="text-muted">{% trans "Start by creating your first grade level." %}</p>
                    <a href="{% url 'students:grade_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add First Grade Level" %}
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Confirm Delete" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "Are you sure you want to delete the grade level" %} "<span id="gradeName"></span>"?</p>
                <p class="text-danger"><small>{% trans "This action cannot be undone and will affect all classes and students in this grade." %}</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <form method="post" id="deleteForm" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">{% trans "Delete" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const resetFilters = document.getElementById('resetFilters');
    const gradeCards = document.querySelectorAll('[data-status]');

    // Search functionality
    searchInput.addEventListener('input', filterGrades);
    statusFilter.addEventListener('change', filterGrades);

    // Reset filters
    resetFilters.addEventListener('click', function() {
        searchInput.value = '';
        statusFilter.value = '';
        filterGrades();
    });

    function filterGrades() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;

        gradeCards.forEach(card => {
            const gradeName = card.querySelector('h5').textContent.toLowerCase();
            const gradeLevel = card.querySelector('.opacity-75').textContent.toLowerCase();
            const status = card.dataset.status;
            
            const matchesSearch = gradeName.includes(searchTerm) || gradeLevel.includes(searchTerm);
            const matchesStatus = !statusValue || status === statusValue;
            
            card.style.display = matchesSearch && matchesStatus ? '' : 'none';
        });
    }
});

function confirmDelete(gradeId, gradeName) {
    document.getElementById('gradeName').textContent = gradeName;
    document.getElementById('deleteForm').action = `/students/grades/${gradeId}/delete/`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
