{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Parents" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .parent-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .parent-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .parent-header {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .btn-primary {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        border: none;
        border-radius: 10px;
    }
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1.5rem;
    }
    .search-box:focus {
        border-color: #fdbb2d;
        box-shadow: 0 0 0 0.2rem rgba(253, 187, 45, 0.25);
    }
    .parent-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">{% trans "Students" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Parents" %}</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-user-friends text-primary me-2"></i>{% trans "Parent Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage parent and guardian information" %}</p>
                </div>
                <div>
                    <a href="{% url 'students:add_parent' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Parent" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control search-box" placeholder="{% trans 'Search parents...' %}" id="searchInput">
                <button class="btn btn-outline-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="d-flex gap-2">
                <select class="form-select" id="relationFilter">
                    <option value="">{% trans "All Relations" %}</option>
                    <option value="father">{% trans "Father" %}</option>
                    <option value="mother">{% trans "Mother" %}</option>
                    <option value="guardian">{% trans "Guardian" %}</option>
                </select>
                <button class="btn btn-outline-secondary" id="resetFilters">
                    <i class="fas fa-undo me-2"></i>{% trans "Reset" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card parent-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-friends fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_parents }}</h4>
                    <p class="text-muted mb-0">{% trans "Total Parents" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card parent-card">
                <div class="card-body text-center">
                    <i class="fas fa-male fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ fathers_count }}</h4>
                    <p class="text-muted mb-0">{% trans "Fathers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card parent-card">
                <div class="card-body text-center">
                    <i class="fas fa-female fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ mothers_count }}</h4>
                    <p class="text-muted mb-0">{% trans "Mothers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card parent-card">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ guardians_count }}</h4>
                    <p class="text-muted mb-0">{% trans "Guardians" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Parents List -->
    <div class="card parent-card">
        <div class="parent-header">
            <div class="card-body">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>{% trans "All Parents" %}
                </h5>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="parentsTable">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Parent" %}</th>
                            <th>{% trans "Relation" %}</th>
                            <th>{% trans "Contact" %}</th>
                            <th>{% trans "Children" %}</th>
                            <th>{% trans "Occupation" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for parent in parents %}
                        <tr data-relation="{{ parent.relationship }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="parent-avatar me-3">
                                        {{ parent.first_name|first }}{{ parent.last_name|first }}
                                    </div>
                                    <div>
                                        <strong>{{ parent.first_name }} {{ parent.last_name }}</strong>
                                        {% if parent.first_name_ar %}
                                            <br><small class="text-muted">{{ parent.first_name_ar }} {{ parent.last_name_ar }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if parent.relationship == 'father' %}
                                    <span class="badge bg-info">{% trans "Father" %}</span>
                                {% elif parent.relationship == 'mother' %}
                                    <span class="badge bg-success">{% trans "Mother" %}</span>
                                {% elif parent.relationship == 'guardian' %}
                                    <span class="badge bg-warning">{% trans "Guardian" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ parent.get_relationship_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    {% if parent.phone %}
                                        <small><i class="fas fa-phone me-1"></i>{{ parent.phone }}</small><br>
                                    {% endif %}
                                    {% if parent.email %}
                                        <small><i class="fas fa-envelope me-1"></i>{{ parent.email }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ parent.children_count }} {% trans "child(ren)" %}</span>
                            </td>
                            <td>
                                {% if parent.occupation %}
                                    <small>{{ parent.occupation }}</small>
                                {% else %}
                                    <small class="text-muted">{% trans "Not specified" %}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'students:parent_detail' parent.pk %}" class="btn btn-sm btn-outline-info" title="{% trans 'View Details' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'students:parent_edit' parent.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete' %}" onclick="confirmDelete({{ parent.pk }}, '{{ parent.first_name }} {{ parent.last_name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">{% trans "No parents found" %}</h5>
                                <p class="text-muted">{% trans "Start by adding parent information." %}</p>
                                <a href="{% url 'students:add_parent' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>{% trans "Add First Parent" %}
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Confirm Delete" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "Are you sure you want to delete" %} "<span id="parentName"></span>"?</p>
                <p class="text-danger"><small>{% trans "This action cannot be undone." %}</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <form method="post" id="deleteForm" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">{% trans "Delete" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const relationFilter = document.getElementById('relationFilter');
    const resetFilters = document.getElementById('resetFilters');
    const table = document.getElementById('parentsTable');
    const rows = table.querySelectorAll('tbody tr');

    // Search functionality
    searchInput.addEventListener('input', filterTable);
    relationFilter.addEventListener('change', filterTable);

    // Reset filters
    resetFilters.addEventListener('click', function() {
        searchInput.value = '';
        relationFilter.value = '';
        filterTable();
    });

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const relationValue = relationFilter.value;

        rows.forEach(row => {
            if (row.cells.length === 1) return; // Skip empty state row
            
            const parentName = row.cells[0].textContent.toLowerCase();
            const contact = row.cells[2].textContent.toLowerCase();
            const relation = row.dataset.relation;
            
            const matchesSearch = parentName.includes(searchTerm) || contact.includes(searchTerm);
            const matchesRelation = !relationValue || relation === relationValue;
            
            row.style.display = matchesSearch && matchesRelation ? '' : 'none';
        });
    }
});

function confirmDelete(parentId, parentName) {
    document.getElementById('parentName').textContent = parentName;
    document.getElementById('deleteForm').action = `/students/parents/${parentId}/delete/`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
