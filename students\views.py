from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta
from .models import (
    Student, Parent, Class, Grade, StudentDocument, VacationRequest,
    StudentInfraction, StudentTransfer, StudentAttachment, ElectronicRegistration
)
from .forms import (
    StudentForm, ParentForm, ClassForm, GradeForm, VacationRequestForm,
    StudentInfractionForm, StudentTransferForm, StudentDocumentForm,
    StudentAttachmentForm, ElectronicRegistrationForm
)

# Student Management Views
class StudentDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'students/student_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current date for filtering
        today = timezone.now().date()
        thirty_days_ago = today - timedelta(days=30)
        current_year = today.year

        # Student statistics
        context['total_students'] = Student.objects.filter(is_active=True).count()
        context['new_admissions'] = Student.objects.filter(
            admission_date__gte=thirty_days_ago,
            is_active=True
        ).count()
        context['active_students'] = Student.objects.filter(
            is_active=True,
            is_graduated=False
        ).count()
        context['graduating_students'] = Student.objects.filter(
            is_active=True,
            current_class__grade__level__gte=12  # Assuming grade 12 is graduating
        ).count()

        # Recent students (last 10)
        context['recent_students'] = Student.objects.filter(
            is_active=True
        ).select_related('current_class', 'current_class__grade').order_by('-admission_date')[:10]

        # Pending tasks counts
        context['pending_admissions'] = ElectronicRegistration.objects.filter(
            status='submitted'
        ).count()
        context['pending_documents'] = StudentDocument.objects.filter(
            is_verified=False
        ).count()
        context['pending_fees'] = Student.objects.filter(
            is_active=True,
            # Add fee-related filtering when finance models are available
        ).count() if hasattr(Student, 'fees') else 0
        context['transfer_requests'] = StudentTransfer.objects.filter(
            status='pending'
        ).count()
        context['id_card_requests'] = Student.objects.filter(
            is_active=True,
            id_card_issued=False
        ).count() if hasattr(Student, 'id_card_issued') else 0

        return context

class StudentListView(LoginRequiredMixin, ListView):
    model = Student
    template_name = 'students/student_list.html'
    context_object_name = 'students'
    paginate_by = 20

    def get_queryset(self):
        queryset = Student.objects.select_related('user', 'parent', 'current_class').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(student_id__icontains=search) |
                Q(admission_number__icontains=search)
            )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Breadcrumb navigation
        context['breadcrumb_items'] = [
            {'title': _('Home'), 'url': reverse('accounts:dashboard')},
            {'title': _('Students'), 'url': None},
        ]

        # Additional statistics
        context['active_students'] = Student.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.filter(is_active=True).count()
        context['new_admissions'] = Student.objects.filter(
            admission_date__gte=timezone.now().date() - timedelta(days=30)
        ).count()

        return context

class StudentDetailView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'students/student_detail.html'
    context_object_name = 'student'

class StudentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.add_student'
    success_url = reverse_lazy('students:list')

    def form_valid(self, form):
        messages.success(self.request, _('Student added successfully!'))
        return super().form_valid(form)

class StudentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.change_student'

    def get_success_url(self):
        return reverse_lazy('students:detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, _('Student updated successfully!'))
        return super().form_valid(form)

class StudentDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = Student
    template_name = 'students/student_confirm_delete.html'
    permission_required = 'students.delete_student'
    success_url = reverse_lazy('students:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Student deleted successfully!'))
        return super().delete(request, *args, **kwargs)

class StudentAdvancedSearchView(LoginRequiredMixin, TemplateView):
    template_name = 'students/advanced_search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grades'] = Grade.objects.all()
        context['classes'] = Class.objects.all()
        return context

class DistributeStudentsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/distribute_students.html'
    permission_required = 'students.change_student'

class StudentTransferView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/student_transfer.html'
    permission_required = 'students.change_student'

class StudentCardsView(LoginRequiredMixin, TemplateView):
    template_name = 'students/student_cards.html'

# Parent Management Views
class ParentListView(LoginRequiredMixin, ListView):
    model = Parent
    template_name = 'students/parent_list.html'
    context_object_name = 'parents'
    paginate_by = 20

class ParentDetailView(LoginRequiredMixin, DetailView):
    model = Parent
    template_name = 'students/parent_detail.html'
    context_object_name = 'parent'

class ParentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Parent
    form_class = ParentForm
    template_name = 'students/parent_form.html'
    permission_required = 'students.add_parent'
    success_url = reverse_lazy('students:parent_list')

class ParentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Parent
    form_class = ParentForm
    template_name = 'students/parent_form.html'
    permission_required = 'students.change_parent'

    def get_success_url(self):
        return reverse_lazy('students:parent_detail', kwargs={'pk': self.object.pk})

class AddParentManualView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/add_parent_manual.html'
    permission_required = 'students.add_parent'

class BulkImportView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/bulk_import.html'
    permission_required = 'students.add_student'

class StudentReportsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/reports.html'
    permission_required = 'students.view_student'

class StudentIDCardsView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = Student
    template_name = 'students/id_cards.html'
    permission_required = 'students.view_student'
    context_object_name = 'students'
    paginate_by = 12

    def get_queryset(self):
        queryset = Student.objects.select_related(
            'user', 'current_class', 'current_class__grade'
        ).filter(is_active=True)

        # Filter by search query
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(student_id__icontains=search)
            )

        # Filter by class
        class_id = self.request.GET.get('class')
        if class_id:
            queryset = queryset.filter(current_class_id=class_id)

        # Filter by grade
        grade_id = self.request.GET.get('grade')
        if grade_id:
            queryset = queryset.filter(current_class__grade_id=grade_id)

        return queryset.order_by('current_class__grade__level', 'current_class__name', 'first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grades'] = Grade.objects.filter(is_active=True).order_by('level')
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')
        context['search_query'] = self.request.GET.get('search', '')
        context['selected_class'] = self.request.GET.get('class', '')
        context['selected_grade'] = self.request.GET.get('grade', '')
        return context

class StudentCertificatesView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/certificates.html'
    permission_required = 'students.view_student'

# Class Management Views
class ClassListView(LoginRequiredMixin, ListView):
    model = Class
    template_name = 'students/class_list.html'
    context_object_name = 'classes'

class ClassDetailView(LoginRequiredMixin, DetailView):
    model = Class
    template_name = 'students/class_detail.html'
    context_object_name = 'class_obj'

class ClassCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Class
    form_class = ClassForm
    template_name = 'students/class_form.html'
    permission_required = 'students.add_class'
    success_url = reverse_lazy('students:class_list')

class ClassUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Class
    form_class = ClassForm
    template_name = 'students/class_form.html'
    permission_required = 'students.change_class'

    def get_success_url(self):
        return reverse_lazy('students:class_detail', kwargs={'pk': self.object.pk})

# Grade Management Views
class GradeListView(LoginRequiredMixin, ListView):
    model = Grade
    template_name = 'students/grade_list.html'
    context_object_name = 'grades'

class GradeCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Grade
    form_class = GradeForm
    template_name = 'students/grade_form.html'
    permission_required = 'students.add_grade'
    success_url = reverse_lazy('students:grade_list')

class GradeUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Grade
    form_class = GradeForm
    template_name = 'students/grade_form.html'
    permission_required = 'students.change_grade'
    success_url = reverse_lazy('students:grade_list')

# Student Affairs Views
class StudentInfractionsView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = StudentInfraction
    template_name = 'students/infractions.html'
    context_object_name = 'infractions'
    permission_required = 'students.view_studentinfraction'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentInfraction.objects.select_related('student', 'reported_by', 'handled_by').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__first_name__icontains=search) |
                Q(student__last_name__icontains=search) |
                Q(description__icontains=search)
            )
        return queryset.order_by('-incident_date')


class StudentInfractionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentInfraction
    form_class = StudentInfractionForm
    template_name = 'students/infraction_form.html'
    permission_required = 'students.add_studentinfraction'
    success_url = reverse_lazy('students:infractions')

    def form_valid(self, form):
        form.instance.reported_by = self.request.user
        messages.success(self.request, _('Infraction recorded successfully!'))
        return super().form_valid(form)


class SuspensionAndBlockView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/suspension_block.html'
    permission_required = 'students.change_student'


class VacationRequestsView(LoginRequiredMixin, ListView):
    model = VacationRequest
    template_name = 'students/vacation_requests.html'
    context_object_name = 'vacation_requests'
    paginate_by = 20

    def get_queryset(self):
        queryset = VacationRequest.objects.select_related('student', 'requested_by', 'approved_by').all()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-created_at')


class VacationRequestCreateView(LoginRequiredMixin, CreateView):
    model = VacationRequest
    form_class = VacationRequestForm
    template_name = 'students/vacation_request_form.html'
    success_url = reverse_lazy('students:vacation_requests')

    def form_valid(self, form):
        form.instance.requested_by = self.request.user
        messages.success(self.request, _('Vacation request submitted successfully!'))
        return super().form_valid(form)


class ApproveVacationRequestsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/approve_vacation.html'
    permission_required = 'students.change_vacationrequest'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['pending_requests'] = VacationRequest.objects.filter(
            status='pending'
        ).select_related('student', 'requested_by')
        return context

    def post(self, request, *args, **kwargs):
        request_id = request.POST.get('request_id')
        action = request.POST.get('action')
        rejection_reason = request.POST.get('rejection_reason', '')

        try:
            vacation_request = VacationRequest.objects.get(id=request_id)
            if action == 'approve':
                vacation_request.status = 'approved'
                vacation_request.approved_by = request.user
                vacation_request.approved_at = timezone.now()
                messages.success(request, _('Vacation request approved successfully!'))
            elif action == 'reject':
                vacation_request.status = 'rejected'
                vacation_request.rejection_reason = rejection_reason
                messages.success(request, _('Vacation request rejected.'))

            vacation_request.save()
        except VacationRequest.DoesNotExist:
            messages.error(request, _('Vacation request not found.'))

        return redirect('students:approve_vacation')


class StudentSecondLanguageView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/second_language.html'
    permission_required = 'students.change_student'

# Documents and Attachments Views
class StudentDocumentsView(LoginRequiredMixin, ListView):
    model = StudentDocument
    template_name = 'students/documents.html'
    context_object_name = 'documents'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentDocument.objects.select_related('student', 'uploaded_by', 'verified_by').all()
        student_id = self.request.GET.get('student')
        document_type = self.request.GET.get('type')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if document_type:
            queryset = queryset.filter(document_type=document_type)

        return queryset.order_by('-created_at')


class StudentDocumentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentDocument
    form_class = StudentDocumentForm
    template_name = 'students/document_form.html'
    permission_required = 'students.add_studentdocument'
    success_url = reverse_lazy('students:documents')

    def form_valid(self, form):
        form.instance.uploaded_by = self.request.user
        messages.success(self.request, _('Document uploaded successfully!'))
        return super().form_valid(form)


class StudentDocumentReceiverView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/document_receiver.html'
    permission_required = 'students.change_student'


class StudentAttachmentsView(LoginRequiredMixin, ListView):
    model = StudentAttachment
    template_name = 'students/attachments.html'
    context_object_name = 'attachments'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentAttachment.objects.select_related('student', 'uploaded_by').all()
        student_id = self.request.GET.get('student')
        attachment_type = self.request.GET.get('type')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if attachment_type:
            queryset = queryset.filter(attachment_type=attachment_type)

        return queryset.order_by('-created_at')


class StudentAttachmentCreateView(LoginRequiredMixin, CreateView):
    model = StudentAttachment
    form_class = StudentAttachmentForm
    template_name = 'students/attachment_form.html'
    success_url = reverse_lazy('students:attachments')

    def form_valid(self, form):
        form.instance.uploaded_by = self.request.user
        messages.success(self.request, _('Attachment uploaded successfully!'))
        return super().form_valid(form)

# Electronic Registration
class ElectronicRegistrationView(LoginRequiredMixin, ListView):
    model = ElectronicRegistration
    template_name = 'students/electronic_registration.html'
    context_object_name = 'registrations'
    paginate_by = 20

    def get_queryset(self):
        queryset = ElectronicRegistration.objects.select_related('desired_grade', 'reviewed_by', 'student').all()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-created_at')


class ElectronicRegistrationCreateView(CreateView):
    model = ElectronicRegistration
    form_class = ElectronicRegistrationForm
    template_name = 'students/electronic_registration_form.html'
    success_url = reverse_lazy('students:electronic_registration')

    def form_valid(self, form):
        form.instance.submitted_at = timezone.now()
        form.instance.status = 'submitted'
        messages.success(self.request, _('Registration submitted successfully! We will review your application.'))
        return super().form_valid(form)


# Student Transfer Views
class StudentTransferListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = StudentTransfer
    template_name = 'students/transfers.html'
    context_object_name = 'transfers'
    permission_required = 'students.view_studenttransfer'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentTransfer.objects.select_related(
            'student', 'from_class', 'to_class', 'requested_by', 'approved_by'
        ).all()
        transfer_type = self.request.GET.get('type')
        status = self.request.GET.get('status')

        if transfer_type:
            queryset = queryset.filter(transfer_type=transfer_type)
        if status:
            queryset = queryset.filter(status=status)

        return queryset.order_by('-transfer_date')


class StudentTransferCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentTransfer
    form_class = StudentTransferForm
    template_name = 'students/transfer_form.html'
    permission_required = 'students.add_studenttransfer'
    success_url = reverse_lazy('students:transfers')

    def form_valid(self, form):
        form.instance.requested_by = self.request.user
        messages.success(self.request, _('Transfer request created successfully!'))
        return super().form_valid(form)

# Settings Views
class StudentSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/settings.html'
    permission_required = 'students.change_student'

class StudentFieldsSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/fields_settings.html'
    permission_required = 'students.change_student'

class StudentTabsSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/tabs_settings.html'
    permission_required = 'students.change_student'
