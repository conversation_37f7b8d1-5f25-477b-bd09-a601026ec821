# Django Settings
DEBUG=True
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
# For SQLite (Development)
DATABASE_URL=sqlite:///db.sqlite3

# For PostgreSQL (Production)
# DATABASE_URL=postgresql://username:password@localhost:5432/school_erp

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security Settings
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Internationalization
LANGUAGE_CODE=en
TIME_ZONE=UTC

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes

# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Cache Settings
CACHE_TTL=300  # 5 minutes

# Logging Level
LOG_LEVEL=INFO

# External Services (Optional)
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.sms-provider.com/send

# Backup Settings
BACKUP_ENABLED=True
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30

# Sync Settings
SYNC_ENABLED=True
SYNC_INTERVAL=300  # 5 minutes
