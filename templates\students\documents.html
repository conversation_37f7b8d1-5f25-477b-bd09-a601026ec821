{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Student Documents" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .document-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .document-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .document-verified { border-left-color: #28a745; }
    .document-unverified { border-left-color: #ffc107; }
    .document-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    .file-preview {
        max-width: 100%;
        max-height: 200px;
        object-fit: cover;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-folder-open me-2"></i>{% trans "Student Documents" %}</h2>
            <p class="text-muted">{% trans "Manage student documents and files" %}</p>
        </div>
        <div>
            <a href="{% url 'students:add_document' %}" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>{% trans "Upload Document" %}
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">{% trans "Search" %}</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="{% trans 'Search by student name or title...' %}"
                           value="{{ request.GET.search }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "Student" %}</label>
                    <select name="student" class="form-select">
                        <option value="">{% trans "All Students" %}</option>
                        <!-- Add student options here -->
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "Document Type" %}</label>
                    <select name="type" class="form-select">
                        <option value="">{% trans "All Types" %}</option>
                        <option value="birth_certificate" {% if request.GET.type == 'birth_certificate' %}selected{% endif %}>{% trans "Birth Certificate" %}</option>
                        <option value="passport" {% if request.GET.type == 'passport' %}selected{% endif %}>{% trans "Passport" %}</option>
                        <option value="medical_record" {% if request.GET.type == 'medical_record' %}selected{% endif %}>{% trans "Medical Record" %}</option>
                        <option value="previous_school_record" {% if request.GET.type == 'previous_school_record' %}selected{% endif %}>{% trans "Previous School Record" %}</option>
                        <option value="photo" {% if request.GET.type == 'photo' %}selected{% endif %}>{% trans "Photo" %}</option>
                        <option value="vaccination_record" {% if request.GET.type == 'vaccination_record' %}selected{% endif %}>{% trans "Vaccination Record" %}</option>
                        <option value="other" {% if request.GET.type == 'other' %}selected{% endif %}>{% trans "Other" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "Verification Status" %}</label>
                    <select name="verified" class="form-select">
                        <option value="">{% trans "All Documents" %}</option>
                        <option value="true" {% if request.GET.verified == 'true' %}selected{% endif %}>{% trans "Verified" %}</option>
                        <option value="false" {% if request.GET.verified == 'false' %}selected{% endif %}>{% trans "Unverified" %}</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>{% trans "Filter" %}
                    </button>
                    <a href="{% url 'students:documents' %}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times me-1"></i>{% trans "Clear" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">{{ total_documents|default:0 }}</h5>
                    <p class="card-text">{% trans "Total Documents" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">{{ verified_documents|default:0 }}</h5>
                    <p class="card-text">{% trans "Verified" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">{{ unverified_documents|default:0 }}</h5>
                    <p class="card-text">{% trans "Unverified" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">{{ recent_uploads|default:0 }}</h5>
                    <p class="card-text">{% trans "Recent Uploads" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Documents Grid -->
    <div class="row">
        {% for document in documents %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card document-card {% if document.is_verified %}document-verified{% else %}document-unverified{% endif %}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">{{ document.title }}</h6>
                        <small class="text-muted">{{ document.student.full_name }}</small>
                    </div>
                    {% if document.is_verified %}
                    <span class="badge bg-success">
                        <i class="fas fa-check me-1"></i>{% trans "Verified" %}
                    </span>
                    {% else %}
                    <span class="badge bg-warning">
                        <i class="fas fa-clock me-1"></i>{% trans "Pending" %}
                    </span>
                    {% endif %}
                </div>
                <div class="card-body text-center">
                    <!-- Document Icon/Preview -->
                    {% if document.file.name|slice:"-4:" == ".jpg" or document.file.name|slice:"-4:" == ".png" or document.file.name|slice:"-5:" == ".jpeg" %}
                        <img src="{{ document.file.url }}" alt="{{ document.title }}" class="file-preview">
                    {% elif document.file.name|slice:"-4:" == ".pdf" %}
                        <i class="fas fa-file-pdf document-icon text-danger"></i>
                    {% elif document.file.name|slice:"-4:" == ".doc" or document.file.name|slice:"-5:" == ".docx" %}
                        <i class="fas fa-file-word document-icon text-primary"></i>
                    {% else %}
                        <i class="fas fa-file document-icon text-secondary"></i>
                    {% endif %}
                    
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Type" %}</small>
                        <strong>{{ document.get_document_type_display }}</strong>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Uploaded" %}</small>
                        <strong>{{ document.created_at|date:"M d, Y" }}</strong>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Uploaded By" %}</small>
                        <strong>{{ document.uploaded_by.get_full_name }}</strong>
                    </div>
                    {% if document.description %}
                    <div class="mt-2">
                        <small class="text-muted d-block">{% trans "Description" %}</small>
                        <p class="small">{{ document.description|truncatewords:10 }}</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group btn-group-sm w-100">
                        <a href="{{ document.file.url }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>{% trans "View" %}
                        </a>
                        <a href="{{ document.file.url }}" download class="btn btn-outline-secondary">
                            <i class="fas fa-download me-1"></i>{% trans "Download" %}
                        </a>
                        {% if not document.is_verified and perms.students.change_studentdocument %}
                        <button class="btn btn-outline-success" onclick="verifyDocument({{ document.id }})">
                            <i class="fas fa-check me-1"></i>{% trans "Verify" %}
                        </button>
                        {% endif %}
                    </div>
                    {% if perms.students.delete_studentdocument %}
                    <div class="mt-2">
                        <button class="btn btn-outline-danger btn-sm w-100" onclick="deleteDocument({{ document.id }})">
                            <i class="fas fa-trash me-1"></i>{% trans "Delete" %}
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">{% trans "No documents found" %}</h4>
                <p class="text-muted">{% trans "No documents match your current filters." %}</p>
                <a href="{% url 'students:add_document' %}" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>{% trans "Upload First Document" %}
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="{% trans 'Documents pagination' %}">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.verified %}&verified={{ request.GET.verified }}{% endif %}">{% trans "First" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.verified %}&verified={{ request.GET.verified }}{% endif %}">{% trans "Previous" %}</a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">
                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                </span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.verified %}&verified={{ request.GET.verified }}{% endif %}">{% trans "Next" %}</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.verified %}&verified={{ request.GET.verified }}{% endif %}">{% trans "Last" %}</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function verifyDocument(documentId) {
    if (confirm('{% trans "Are you sure you want to verify this document?" %}')) {
        // Add AJAX call to verify document
        fetch(`/students/documents/${documentId}/verify/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error verifying document" %}');
            }
        });
    }
}

function deleteDocument(documentId) {
    if (confirm('{% trans "Are you sure you want to delete this document? This action cannot be undone." %}')) {
        // Add AJAX call to delete document
        fetch(`/students/documents/${documentId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error deleting document" %}');
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name="type"], select[name="verified"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}
