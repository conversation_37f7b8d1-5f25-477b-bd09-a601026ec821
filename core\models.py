from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator


class BaseModel(models.Model):
    """
    Abstract base model with common fields
    """
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        abstract = True


class School(BaseModel):
    """
    School information model
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('School Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('School Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('School Code')
    )

    address = models.TextField(
        verbose_name=_('Address')
    )

    phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Phone Number')
    )

    email = models.EmailField(
        verbose_name=_('Email')
    )

    website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Website')
    )

    logo = models.ImageField(
        upload_to='school/logos/',
        blank=True,
        null=True,
        verbose_name=_('Logo')
    )

    principal_name = models.CharField(
        max_length=100,
        verbose_name=_('Principal Name')
    )

    established_date = models.DateField(
        verbose_name=_('Established Date')
    )

    license_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('License Number')
    )

    tax_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Tax Number')
    )

    class Meta:
        verbose_name = _('School')
        verbose_name_plural = _('Schools')

    def __str__(self):
        return self.name


class AcademicYear(BaseModel):
    """
    Academic year model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Academic Year Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_current = models.BooleanField(
        default=False,
        verbose_name=_('Is Current Year')
    )

    class Meta:
        verbose_name = _('Academic Year')
        verbose_name_plural = _('Academic Years')
        ordering = ['-start_date']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one academic year is current
            AcademicYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Semester(BaseModel):
    """
    Semester model
    """
    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='semesters',
        verbose_name=_('Academic Year')
    )

    name = models.CharField(
        max_length=50,
        verbose_name=_('Semester Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_current = models.BooleanField(
        default=False,
        verbose_name=_('Is Current Semester')
    )

    class Meta:
        verbose_name = _('Semester')
        verbose_name_plural = _('Semesters')
        ordering = ['academic_year', 'start_date']

    def __str__(self):
        return f"{self.academic_year.name} - {self.name}"
