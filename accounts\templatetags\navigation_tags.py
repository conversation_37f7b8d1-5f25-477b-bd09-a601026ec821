from django import template
from django.urls import reverse, resolve
from django.utils.translation import gettext_lazy as _
from django.utils.safestring import mark_safe

register = template.Library()

@register.simple_tag(takes_context=True)
def breadcrumb_nav(context):
    """Generate breadcrumb navigation based on current URL"""
    request = context['request']
    current_url = request.resolver_match
    
    if not current_url:
        return ""
    
    breadcrumbs = []
    
    # Home breadcrumb
    breadcrumbs.append({
        'name': _('Home'),
        'url': reverse('accounts:dashboard'),
        'icon': 'fas fa-home',
        'active': False
    })
    
    # App-specific breadcrumbs
    app_name = current_url.app_name
    url_name = current_url.url_name
    
    if app_name == 'students':
        breadcrumbs.append({
            'name': _('Students'),
            'url': reverse('students:dashboard'),
            'icon': 'fas fa-user-graduate',
            'active': url_name == 'dashboard'
        })
        
        if url_name in ['student_list', 'student_create', 'student_detail', 'student_edit']:
            breadcrumbs.append({
                'name': _('Student Management'),
                'url': reverse('students:student_list'),
                'icon': 'fas fa-users',
                'active': url_name in ['student_list', 'student_create']
            })
            
        if url_name in ['student_detail', 'student_edit']:
            breadcrumbs.append({
                'name': _('Student Details'),
                'url': '#',
                'icon': 'fas fa-user',
                'active': True
            })
    
    elif app_name == 'academics':
        breadcrumbs.append({
            'name': _('Academics'),
            'url': reverse('academics:dashboard'),
            'icon': 'fas fa-graduation-cap',
            'active': url_name == 'dashboard'
        })
        
        if url_name in ['subjects', 'subject_create', 'subject_detail', 'subject_edit']:
            breadcrumbs.append({
                'name': _('Subjects'),
                'url': reverse('academics:subjects'),
                'icon': 'fas fa-book',
                'active': url_name in ['subjects', 'subject_create']
            })
            
        elif url_name in ['schedules', 'schedule_create', 'schedule_detail', 'schedule_edit', 'timetable']:
            breadcrumbs.append({
                'name': _('Schedules'),
                'url': reverse('academics:schedules'),
                'icon': 'fas fa-calendar',
                'active': url_name in ['schedules', 'schedule_create']
            })
            
        elif url_name in ['exams', 'exam_add', 'exam_detail', 'exam_edit']:
            breadcrumbs.append({
                'name': _('Exams'),
                'url': reverse('academics:exams'),
                'icon': 'fas fa-clipboard-list',
                'active': url_name in ['exams', 'exam_add']
            })
            
        elif url_name in ['grades', 'grade_create', 'grade_detail', 'grade_update']:
            breadcrumbs.append({
                'name': _('Grades'),
                'url': reverse('academics:grades'),
                'icon': 'fas fa-star',
                'active': url_name in ['grades', 'grade_create']
            })
            
        elif url_name in ['attendance', 'attendance_create', 'attendance_detail', 'attendance_update']:
            breadcrumbs.append({
                'name': _('Attendance'),
                'url': reverse('academics:attendance'),
                'icon': 'fas fa-user-check',
                'active': url_name in ['attendance', 'attendance_create']
            })
    
    elif app_name == 'hr':
        breadcrumbs.append({
            'name': _('Human Resources'),
            'url': reverse('hr:dashboard'),
            'icon': 'fas fa-users-cog',
            'active': url_name == 'dashboard'
        })
        
        if url_name in ['employees', 'employee_create', 'employee_detail', 'employee_edit']:
            breadcrumbs.append({
                'name': _('Employees'),
                'url': reverse('hr:employees'),
                'icon': 'fas fa-users',
                'active': url_name in ['employees', 'employee_create']
            })
            
        elif url_name in ['departments', 'department_create', 'department_detail', 'department_edit']:
            breadcrumbs.append({
                'name': _('Departments'),
                'url': reverse('hr:departments'),
                'icon': 'fas fa-building',
                'active': url_name in ['departments', 'department_create']
            })
    
    elif app_name == 'finance':
        breadcrumbs.append({
            'name': _('Finance'),
            'url': reverse('finance:dashboard'),
            'icon': 'fas fa-money-bill-wave',
            'active': url_name == 'dashboard'
        })
    
    elif app_name == 'reports':
        breadcrumbs.append({
            'name': _('Reports'),
            'url': reverse('reports:dashboard'),
            'icon': 'fas fa-chart-bar',
            'active': url_name == 'dashboard'
        })
    
    elif app_name == 'core':
        breadcrumbs.append({
            'name': _('Administration'),
            'url': reverse('core:dashboard'),
            'icon': 'fas fa-cog',
            'active': url_name == 'dashboard'
        })
    
    # Generate HTML
    html = '<nav aria-label="breadcrumb"><ol class="breadcrumb modern-breadcrumb">'
    
    for i, crumb in enumerate(breadcrumbs):
        if i == len(breadcrumbs) - 1:  # Last item
            html += f'''
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="{crumb['icon']} me-1"></i>
                    {crumb['name']}
                </li>
            '''
        else:
            html += f'''
                <li class="breadcrumb-item">
                    <a href="{crumb['url']}" class="breadcrumb-link">
                        <i class="{crumb['icon']} me-1"></i>
                        {crumb['name']}
                    </a>
                </li>
            '''
    
    html += '</ol></nav>'
    
    return mark_safe(html)

@register.simple_tag(takes_context=True)
def section_nav(context):
    """Generate section navigation for easy switching between modules"""
    request = context['request']
    user = request.user
    
    if not user.is_authenticated:
        return ""
    
    sections = [
        {
            'name': _('Dashboard'),
            'url': reverse('accounts:dashboard'),
            'icon': 'fas fa-home',
            'color': 'primary',
            'active': request.resolver_match.app_name == 'accounts'
        },
        {
            'name': _('Students'),
            'url': reverse('students:dashboard'),
            'icon': 'fas fa-user-graduate',
            'color': 'success',
            'active': request.resolver_match.app_name == 'students'
        },
        {
            'name': _('Academics'),
            'url': reverse('academics:dashboard'),
            'icon': 'fas fa-graduation-cap',
            'color': 'info',
            'active': request.resolver_match.app_name == 'academics'
        },
        {
            'name': _('HR'),
            'url': reverse('hr:dashboard'),
            'icon': 'fas fa-users-cog',
            'color': 'warning',
            'active': request.resolver_match.app_name == 'hr'
        },
        {
            'name': _('Finance'),
            'url': reverse('finance:dashboard'),
            'icon': 'fas fa-money-bill-wave',
            'color': 'danger',
            'active': request.resolver_match.app_name == 'finance'
        },
        {
            'name': _('Reports'),
            'url': reverse('reports:dashboard'),
            'icon': 'fas fa-chart-bar',
            'color': 'secondary',
            'active': request.resolver_match.app_name == 'reports'
        }
    ]
    
    # Add admin section for admin users
    if user.user_type == 'admin':
        sections.append({
            'name': _('Admin'),
            'url': reverse('core:dashboard'),
            'icon': 'fas fa-cog',
            'color': 'dark',
            'active': request.resolver_match.app_name == 'core'
        })
    
    # Generate HTML
    html = '<div class="section-nav-container"><div class="section-nav">'
    
    for section in sections:
        active_class = 'active' if section['active'] else ''
        html += f'''
            <a href="{section['url']}" class="section-nav-item {active_class}" data-color="{section['color']}">
                <div class="section-nav-icon">
                    <i class="{section['icon']}"></i>
                </div>
                <span class="section-nav-label">{section['name']}</span>
            </a>
        '''
    
    html += '</div></div>'
    
    return mark_safe(html)

@register.simple_tag(takes_context=True)
def current_section_title(context):
    """Get the current section title"""
    request = context['request']
    app_name = request.resolver_match.app_name if request.resolver_match else None
    
    titles = {
        'accounts': _('Dashboard'),
        'students': _('Student Management'),
        'academics': _('Academic Management'),
        'hr': _('Human Resources'),
        'finance': _('Financial Management'),
        'reports': _('Reports & Analytics'),
        'core': _('System Administration')
    }
    
    return titles.get(app_name, _('School ERP System'))
